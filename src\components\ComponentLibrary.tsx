import React, { useState } from 'react';
import { Eye, Code, Copy, Download, Package, Menu, X, ChevronDown, AlertCircle, CheckCircle, Info, XCircle } from 'lucide-react';

interface Component {
  id: string;
  name: string;
  description: string;
  code: string;
  preview: React.ReactNode;
}

export const ComponentLibrary = () => {
  const [selectedComponent, setSelectedComponent] = useState<Component | null>(null);
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview');

  const components: Component[] = [
    {
      id: 'button',
      name: 'But<PERSON>',
      description: 'A customizable button component with variants',
      code: `const Button = ({ children, variant = 'primary', ...props }) => {
  const baseClasses = 'px-4 py-2 rounded-lg font-medium transition-colors';
  const variants = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white'
  };
  
  return (
    <button className={\`\${baseClasses} \${variants[variant]}\`} {...props}>
      {children}
    </button>
  );
};`,
      preview: (
        <div className="space-y-4">
          <button className="px-4 py-2 rounded-lg font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white">
            Primary Button
          </button>
          <button className="px-4 py-2 rounded-lg font-medium transition-colors bg-gray-600 hover:bg-gray-700 text-white">
            Secondary Button
          </button>
          <button className="px-4 py-2 rounded-lg font-medium transition-colors border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white">
            Outline Button
          </button>
        </div>
      )
    },
    {
      id: 'card',
      name: 'Card',
      description: 'A flexible card component for content display',
      code: `const Card = ({ title, children, className = '' }) => {
  return (
    <div className={\`bg-white rounded-lg shadow-md p-6 \${className}\`}>
      {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
      {children}
    </div>
  );
};`,
      preview: (
        <div className="bg-white rounded-lg shadow-md p-6 text-gray-900 max-w-sm">
          <h3 className="text-lg font-semibold mb-4">Card Title</h3>
          <p>This is a sample card component with some content inside.</p>
        </div>
      )
    },
    {
      id: 'input',
      name: 'Input',
      description: 'A styled input component with label and validation',
      code: `const Input = ({ label, error, ...props }) => {
  return (
    <div className="mb-4">
      {label && <label className="block text-sm font-medium mb-2">{label}</label>}
      <input
        className={\`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 \${
          error ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
        }\`}
        {...props}
      />
      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </div>
  );
};`,
      preview: (
        <div className="space-y-4 max-w-sm">
          <div>
            <label className="block text-sm font-medium mb-2 text-white">Email</label>
            <input
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your email"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2 text-white">Password</label>
            <input
              type="password"
              className="w-full px-3 py-2 border border-red-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Enter password"
            />
            <p className="text-red-500 text-sm mt-1">Password is required</p>
          </div>
        </div>
      )
    },
    {
      id: 'navbar',
      name: 'Navigation Bar',
      description: 'A responsive navigation bar with mobile menu',
      code: `const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <nav className="bg-gray-800 text-white">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <div className="text-xl font-bold">Logo</div>
          </div>
          
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <a href="#" className="px-3 py-2 rounded-md text-sm font-medium bg-gray-900">Home</a>
              <a href="#" className="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700">About</a>
              <a href="#" className="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700">Services</a>
              <a href="#" className="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700">Contact</a>
            </div>
          </div>
          
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md hover:bg-gray-700"
            >
              {isOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </div>
      
      {isOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <a href="#" className="block px-3 py-2 rounded-md text-base font-medium bg-gray-900">Home</a>
            <a href="#" className="block px-3 py-2 rounded-md text-base font-medium hover:bg-gray-700">About</a>
            <a href="#" className="block px-3 py-2 rounded-md text-base font-medium hover:bg-gray-700">Services</a>
            <a href="#" className="block px-3 py-2 rounded-md text-base font-medium hover:bg-gray-700">Contact</a>
          </div>
        </div>
      )}
    </nav>
  );
};`,
      preview: (
        <nav className="bg-gray-800 text-white">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <div className="text-xl font-bold">Logo</div>
              </div>
              
              <div className="hidden md:block">
                <div className="ml-10 flex items-baseline space-x-4">
                  <a href="#" className="px-3 py-2 rounded-md text-sm font-medium bg-gray-900">Home</a>
                  <a href="#" className="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700">About</a>
                  <a href="#" className="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700">Services</a>
                  <a href="#" className="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700">Contact</a>
                </div>
              </div>
              
              <div className="md:hidden">
                <button className="inline-flex items-center justify-center p-2 rounded-md hover:bg-gray-700">
                  <Menu size={24} />
                </button>
              </div>
            </div>
          </div>
        </nav>
      )
    },
    {
      id: 'dropdown',
      name: 'Dropdown Menu',
      description: 'A dropdown menu component with animations',
      code: `const Dropdown = ({ trigger, items }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-gray-700 hover:bg-gray-600"
      >
        <span>{trigger}</span>
        <ChevronDown size={16} className={\`transform transition-transform \${isOpen ? 'rotate-180' : ''}\`} />
      </button>
      
      {isOpen && (
        <div className="absolute mt-2 w-48 rounded-md shadow-lg bg-gray-700 ring-1 ring-black ring-opacity-5">
          <div className="py-1">
            {items.map((item, index) => (
              <a
                key={index}
                href={item.href}
                className="block px-4 py-2 text-sm hover:bg-gray-600"
              >
                {item.label}
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};`,
      preview: (
        <div className="relative">
          <button className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-gray-700 hover:bg-gray-600">
            <span>Options</span>
            <ChevronDown size={16} />
          </button>
        </div>
      )
    },
    {
      id: 'alert',
      name: 'Alert',
      description: 'Alert components for different types of messages',
      code: `const Alert = ({ type = 'info', message }) => {
  const variants = {
    success: {
      icon: <CheckCircle className="w-5 h-5 text-green-400" />,
      classes: 'bg-green-900/50 text-green-400 border-green-500'
    },
    error: {
      icon: <XCircle className="w-5 h-5 text-red-400" />,
      classes: 'bg-red-900/50 text-red-400 border-red-500'
    },
    warning: {
      icon: <AlertCircle className="w-5 h-5 text-yellow-400" />,
      classes: 'bg-yellow-900/50 text-yellow-400 border-yellow-500'
    },
    info: {
      icon: <Info className="w-5 h-5 text-blue-400" />,
      classes: 'bg-blue-900/50 text-blue-400 border-blue-500'
    }
  };
  
  const variant = variants[type];
  
  return (
    <div className={\`p-4 rounded-lg border \${variant.classes}\`}>
      <div className="flex items-center">
        {variant.icon}
        <p className="ml-3">{message}</p>
      </div>
    </div>
  );
};`,
      preview: (
        <div className="space-y-4">
          <div className="p-4 rounded-lg border bg-green-900/50 text-green-400 border-green-500">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <p className="ml-3">Operation completed successfully!</p>
            </div>
          </div>
          <div className="p-4 rounded-lg border bg-red-900/50 text-red-400 border-red-500">
            <div className="flex items-center">
              <XCircle className="w-5 h-5 text-red-400" />
              <p className="ml-3">An error occurred. Please try again.</p>
            </div>
          </div>
          <div className="p-4 rounded-lg border bg-yellow-900/50 text-yellow-400 border-yellow-500">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-yellow-400" />
              <p className="ml-3">Please review your information before proceeding.</p>
            </div>
          </div>
          <div className="p-4 rounded-lg border bg-blue-900/50 text-blue-400 border-blue-500">
            <div className="flex items-center">
              <Info className="w-5 h-5 text-blue-400" />
              <p className="ml-3">New features are available!</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'form',
      name: 'Form',
      description: 'A complete form component with validation',
      code: `const Form = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  
  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-md">
      <div>
        <label className="block text-sm font-medium mb-2">Name</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-2">Email</label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-2">Message</label>
        <textarea
          value={formData.message}
          onChange={(e) => setFormData({ ...formData, message: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={4}
          required
        />
      </div>
      
      <button
        type="submit"
        className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
      >
        Submit
      </button>
    </form>
  );
};`,
      preview: (
        <form className="space-y-4 max-w-md">
          <div>
            <label className="block text-sm font-medium mb-2 text-white">Name</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2 text-white">Email</label>
            <input
              type="email"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your email"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2 text-white">Message</label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={4}
              placeholder="Enter your message"
            />
          </div>
          
          <button
            type="submit"
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Submit
          </button>
        </form>
      )
    }
  ];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="h-screen flex">
      <div className="w-1/3 bg-gray-800 border-r border-gray-700 overflow-y-auto">
        <div className="p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold">Component Library</h2>
          <p className="text-gray-400 text-sm">Browse and use pre-built components</p>
        </div>
        
        <div className="p-4">
          {components.map((component) => (
            <div
              key={component.id}
              onClick={() => setSelectedComponent(component)}
              className={`p-4 rounded-lg cursor-pointer transition-colors mb-2 ${
                selectedComponent?.id === component.id
                  ? 'bg-blue-600'
                  : 'bg-gray-700 hover:bg-gray-600'
              }`}
            >
              <h3 className="font-medium">{component.name}</h3>
              <p className="text-sm text-gray-400 mt-1">{component.description}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        {selectedComponent ? (
          <>
            <div className="bg-gray-800 border-b border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold">{selectedComponent.name}</h3>
                  <p className="text-gray-400">{selectedComponent.description}</p>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setViewMode('preview')}
                    className={`flex items-center gap-2 px-4 py-2 rounded transition-colors ${
                      viewMode === 'preview' ? 'bg-blue-600' : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    <Eye size={16} />
                    Preview
                  </button>
                  <button
                    onClick={() => setViewMode('code')}
                    className={`flex items-center gap-2 px-4 py-2 rounded transition-colors ${
                      viewMode === 'code' ? 'bg-blue-600' : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    <Code size={16} />
                    Code
                  </button>
                  <button
                    onClick={() => copyToClipboard(selectedComponent.code)}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
                  >
                    <Copy size={16} />
                    Copy
                  </button>
                </div>
              </div>
            </div>

            <div className="flex-1 p-8">
              {viewMode === 'preview' ? (
                <div className="flex items-center justify-center min-h-96">
                  {selectedComponent.preview}
                </div>
              ) : (
                <pre className="bg-gray-900 p-4 rounded-lg overflow-auto h-full">
                  <code className="text-green-400">{selectedComponent.code}</code>
                </pre>
              )}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Package className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Select a Component</h3>
              <p className="text-gray-400">Choose a component from the library to view its preview and code</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
