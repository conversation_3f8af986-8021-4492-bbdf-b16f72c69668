
import React, { useState, useEffect } from 'react';
import { Search, File, Package, Code } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface SearchResult {
  id: string;
  type: 'component' | 'snippet' | 'file';
  title: string;
  description: string;
  content?: string;
}

export const GlobalSearch = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);

  // Mock data
  const mockData: SearchResult[] = [
    {
      id: '1',
      type: 'component',
      title: 'Button Component',
      description: 'Reusable button with variants',
      content: 'Primary, secondary, outline variants'
    },
    {
      id: '2',
      type: 'snippet',
      title: 'useState Hook',
      description: 'React state management',
      content: 'const [state, setState] = useState()'
    },
    {
      id: '3',
      type: 'file',
      title: 'Dashboard.tsx',
      description: 'Main dashboard component',
      content: 'Dashboard component with metrics'
    },
    {
      id: '4',
      type: 'component',
      title: 'Modal Component',
      description: 'Overlay dialog component',
      content: 'Backdrop, close button, responsive'
    },
    {
      id: '5',
      type: 'snippet',
      title: 'API Fetch',
      description: 'Fetch data from API',
      content: 'fetch().then().catch()'
    },
  ];

  useEffect(() => {
    if (query.trim()) {
      const filtered = mockData.filter(item =>
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase()) ||
        item.content?.toLowerCase().includes(query.toLowerCase())
      );
      setResults(filtered);
    } else {
      setResults([]);
    }
  }, [query]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'k') {
        e.preventDefault();
        setIsOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const getIcon = (type: string) => {
    switch (type) {
      case 'component': return <Package className="h-4 w-4 text-blue-500" />;
      case 'snippet': return <Code className="h-4 w-4 text-green-500" />;
      case 'file': return <File className="h-4 w-4 text-purple-500" />;
      default: return <Search className="h-4 w-4" />;
    }
  };

  const getBadgeColor = (type: string) => {
    switch (type) {
      case 'component': return 'bg-blue-100 text-blue-800';
      case 'snippet': return 'bg-green-100 text-green-800';
      case 'file': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2 min-w-48 justify-start text-gray-500">
          <Search className="h-4 w-4" />
          Search everything...
          <Badge variant="secondary" className="ml-auto text-xs">Ctrl+K</Badge>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl p-0">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center px-4 py-3">
            <Search className="h-4 w-4 text-gray-400 mr-3" />
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search components, snippets, files..."
              className="border-0 focus-visible:ring-0 text-base"
              autoFocus
            />
          </div>
        </div>
        
        <div className="max-h-96 overflow-y-auto">
          {query.trim() === '' ? (
            <div className="p-8 text-center text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium mb-2">Search Everything</p>
              <p className="text-sm">Find components, code snippets, and files quickly</p>
              <div className="flex items-center justify-center gap-4 mt-4 text-xs">
                <div className="flex items-center gap-1">
                  <Package className="h-3 w-3 text-blue-500" />
                  Components
                </div>
                <div className="flex items-center gap-1">
                  <Code className="h-3 w-3 text-green-500" />
                  Snippets
                </div>
                <div className="flex items-center gap-1">
                  <File className="h-3 w-3 text-purple-500" />
                  Files
                </div>
              </div>
            </div>
          ) : results.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <p>No results found for "{query}"</p>
            </div>
          ) : (
            <div className="p-2">
              {results.map((result) => (
                <div
                  key={result.id}
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                  onClick={() => {
                    setIsOpen(false);
                    setQuery('');
                  }}
                >
                  {getIcon(result.type)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{result.title}</span>
                      <Badge className={`text-xs ${getBadgeColor(result.type)}`}>
                        {result.type}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {result.description}
                    </p>
                    {result.content && (
                      <p className="text-xs text-gray-500 mt-1 font-mono">
                        {result.content}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
