
import React, { useState, useEffect } from 'react';
import { Users, Video, MessageSquare, Share, UserPlus, Crown, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface User {
  id: string;
  name: string;
  avatar: string;
  role: 'owner' | 'editor' | 'viewer';
  isOnline: boolean;
  cursor?: { x: number; y: number };
}

interface Change {
  id: string;
  userId: string;
  timestamp: Date;
  type: 'edit' | 'add' | 'delete';
  file: string;
  description: string;
}

export const Collaboration = () => {
  const [activeUsers, setActiveUsers] = useState<User[]>([
    {
      id: '1',
      name: 'You',
      avatar: '👨‍💻',
      role: 'owner',
      isOnline: true
    },
    {
      id: '2',
      name: '<PERSON>',
      avatar: '👩‍💻',
      role: 'editor',
      isOnline: true,
      cursor: { x: 450, y: 200 }
    },
    {
      id: '3',
      name: '<PERSON>',
      avatar: '👨‍🎨',
      role: 'viewer',
      isOnline: false
    }
  ]);

  const [recentChanges, setRecentChanges] = useState<Change[]>([
    {
      id: '1',
      userId: '2',
      timestamp: new Date(Date.now() - 300000),
      type: 'edit',
      file: 'components/Button.tsx',
      description: 'Updated button styling'
    },
    {
      id: '2',
      userId: '1',
      timestamp: new Date(Date.now() - 600000),
      type: 'add',
      file: 'pages/Dashboard.tsx',
      description: 'Added new dashboard component'
    }
  ]);

  const [inviteEmail, setInviteEmail] = useState('');
  const [showVideoCall, setShowVideoCall] = useState(false);

  const handleInvite = () => {
    if (inviteEmail) {
      console.log('Inviting user:', inviteEmail);
      setInviteEmail('');
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return <Crown className="w-3 h-3 text-yellow-500" />;
      case 'editor': return <Users className="w-3 h-3 text-blue-500" />;
      case 'viewer': return <Eye className="w-3 h-3 text-gray-500" />;
      default: return null;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'owner': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'editor': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'viewer': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">Real-Time Collaboration</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Collaborate with your team in real-time
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Active Users */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Users className="w-5 h-5" />
              Active Users ({activeUsers.filter(u => u.isOnline).length})
            </h2>
            
            <div className="space-y-3 mb-4">
              {activeUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <Avatar className="w-8 h-8 bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-sm">
                        {user.avatar}
                      </Avatar>
                      {user.isOnline && (
                        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-sm">{user.name}</p>
                      <Badge variant="secondary" className={`text-xs ${getRoleBadgeColor(user.role)}`}>
                        {getRoleIcon(user.role)}
                        <span className="ml-1 capitalize">{user.role}</span>
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="border-t pt-4">
              <div className="flex gap-2 mb-3">
                <Input
                  placeholder="Invite by email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={handleInvite} size="sm">
                  <UserPlus className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => setShowVideoCall(!showVideoCall)}
                >
                  <Video className="w-4 h-4 mr-2" />
                  Video Call
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Share className="w-4 h-4 mr-2" />
                  Share Link
                </Button>
              </div>
            </div>
          </div>

          {/* Recent Changes */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              Recent Changes
            </h2>
            
            <div className="space-y-4">
              {recentChanges.map((change) => {
                const user = activeUsers.find(u => u.id === change.userId);
                return (
                  <div key={change.id} className="border-l-2 border-blue-500 pl-3">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium">{user?.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {change.type}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                      {change.description}
                    </p>
                    <p className="text-xs text-gray-500">
                      {change.file} • {change.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Live Chat */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              Team Chat
            </h2>
            
            <div className="h-64 bg-gray-50 dark:bg-gray-700 rounded p-3 mb-3 overflow-y-auto">
              <div className="space-y-2">
                <div className="text-sm">
                  <span className="font-medium text-blue-600">Sarah:</span>
                  <span className="ml-2">Working on the button component updates</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium text-green-600">You:</span>
                  <span className="ml-2">Looks good! Can you also update the hover states?</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium text-blue-600">Sarah:</span>
                  <span className="ml-2">Sure! On it 👍</span>
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Input placeholder="Type a message..." className="flex-1" />
              <Button size="sm">Send</Button>
            </div>
          </div>
        </div>

        {/* Video Call Modal */}
        {showVideoCall && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96">
              <h3 className="text-lg font-semibold mb-4">Video Call</h3>
              <div className="bg-gray-900 rounded-lg h-48 flex items-center justify-center mb-4">
                <Video className="w-12 h-12 text-gray-500" />
              </div>
              <div className="flex gap-2">
                <Button variant="outline" className="flex-1">Mute</Button>
                <Button variant="outline" className="flex-1">Camera</Button>
                <Button 
                  variant="destructive" 
                  className="flex-1"
                  onClick={() => setShowVideoCall(false)}
                >
                  End Call
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
