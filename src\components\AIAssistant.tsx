
import React, { useState } from 'react';
import { Bot, Send, Copy, Download, MessageSquare, Code, FileText, CheckCircle } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface AIService {
  id: string;
  name: string;
  description: string;
  prompt: string;
}

export const AIAssistant = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [selectedService, setSelectedService] = useState('code-generation');
  const [isLoading, setIsLoading] = useState(false);

  const aiServices: AIService[] = [
    {
      id: 'code-generation',
      name: 'Code Generation',
      description: 'Generate React components, functions, and utilities',
      prompt: 'You are an expert React/TypeScript developer. Generate clean, production-ready code based on the user request. Include proper TypeScript types and follow best practices.'
    },
    {
      id: 'dummy-text',
      name: 'Dummy Text Generator',
      description: 'Generate realistic dummy content for testing',
      prompt: 'Generate realistic dummy text, data, or content based on the user request. Make it contextually appropriate and varied.'
    },
    {
      id: 'code-review',
      name: 'Code Review',
      description: 'Get detailed code review and suggestions',
      prompt: 'You are an experienced code reviewer. Analyze the provided code for best practices, performance, security, and maintainability. Provide specific suggestions for improvement.'
    }
  ];

  const currentService = aiServices.find(service => service.id === selectedService) || aiServices[0];

  const sendMessage = async () => {
    if (!input.trim() || !apiKey.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Using Groq API as example (you can switch to OpenAI)
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'llama3-8b-8192',
          messages: [
            {
              role: 'system',
              content: currentService.prompt
            },
            {
              role: 'user',
              content: input
            }
          ],
          temperature: 0.7,
          max_tokens: 1000,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const data = await response.json();
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: data.choices[0].message.content,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('AI API Error:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'Sorry, I encountered an error. Please check your API key and try again.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const downloadAsFile = (content: string, filename: string) => {
    const element = document.createElement('a');
    const file = new Blob([content], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  return (
    <div className="h-screen flex">
      <div className="w-1/4 bg-gray-800 border-r border-gray-700 overflow-y-auto">
        <div className="p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold mb-2">AI Assistant</h2>
          <p className="text-gray-400 text-sm">Powered by AI for code generation, review, and content creation</p>
        </div>

        <div className="p-4 space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">API Key</label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter your Groq/OpenAI API key"
              className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-400 outline-none text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">AI Service</label>
            <select
              value={selectedService}
              onChange={(e) => setSelectedService(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-400 outline-none"
            >
              {aiServices.map((service) => (
                <option key={service.id} value={service.id}>
                  {service.name}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-400 mt-1">{currentService.description}</p>
          </div>

          <div className="space-y-2">
            {aiServices.map((service) => (
              <div
                key={service.id}
                onClick={() => setSelectedService(service.id)}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedService === service.id
                    ? 'bg-blue-600'
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                <div className="flex items-center gap-2 mb-1">
                  {service.id === 'code-generation' && <Code className="w-4 h-4" />}
                  {service.id === 'dummy-text' && <FileText className="w-4 h-4" />}
                  {service.id === 'code-review' && <CheckCircle className="w-4 h-4" />}
                  <h3 className="font-medium text-sm">{service.name}</h3>
                </div>
                <p className="text-xs text-gray-400">{service.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        <div className="bg-gray-800 border-b border-gray-700 p-4">
          <div className="flex items-center gap-2">
            <Bot className="w-6 h-6 text-blue-400" />
            <h3 className="text-xl font-semibold">{currentService.name}</h3>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageSquare className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Start a Conversation</h3>
                <p className="text-gray-400">Ask AI to generate code, review your code, or create dummy content</p>
              </div>
            </div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-3xl p-4 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-white'
                  }`}
                >
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1">
                      <pre className="whitespace-pre-wrap font-mono text-sm">{message.content}</pre>
                    </div>
                    {message.type === 'assistant' && (
                      <div className="flex gap-1">
                        <button
                          onClick={() => copyToClipboard(message.content)}
                          className="p-1 hover:bg-gray-600 rounded transition-colors"
                          title="Copy"
                        >
                          <Copy size={14} />
                        </button>
                        <button
                          onClick={() => downloadAsFile(message.content, `ai-response-${message.id}.txt`)}
                          className="p-1 hover:bg-gray-600 rounded transition-colors"
                          title="Download"
                        >
                          <Download size={14} />
                        </button>
                      </div>
                    )}
                  </div>
                  <div className="text-xs opacity-70 mt-2">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))
          )}
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
                  <span className="text-sm">AI is thinking...</span>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="bg-gray-800 border-t border-gray-700 p-4">
          <div className="flex gap-2">
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={`Ask ${currentService.name.toLowerCase()}...`}
              className="flex-1 bg-gray-700 border-gray-600 text-white resize-none"
              rows={3}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
            />
            <button
              onClick={sendMessage}
              disabled={!input.trim() || !apiKey.trim() || isLoading}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded transition-colors"
            >
              <Send size={20} />
            </button>
          </div>
          <p className="text-xs text-gray-400 mt-2">
            Press Shift+Enter for new line, Enter to send
          </p>
        </div>
      </div>
    </div>
  );
};
