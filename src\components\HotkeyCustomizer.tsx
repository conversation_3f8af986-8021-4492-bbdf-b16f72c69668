
import React, { useState } from 'react';
import { Keyboard, Edit, Save, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';

interface Hotkey {
  id: string;
  action: string;
  keys: string[];
  description: string;
}

export const HotkeyCustomizer = () => {
  const [hotkeys, setHotkeys] = useState<Hotkey[]>([
    { id: '1', action: 'Save File', keys: ['Ctrl', 'S'], description: 'Save current file' },
    { id: '2', action: 'New File', keys: ['Ctrl', 'N'], description: 'Create new file' },
    { id: '3', action: 'Search', keys: ['Ctrl', 'K'], description: 'Open global search' },
    { id: '4', action: 'Command Palette', keys: ['Ctrl', 'Shift', 'P'], description: 'Open command palette' },
    { id: '5', action: 'Toggle Sidebar', keys: ['Ctrl', 'B'], description: 'Toggle sidebar visibility' },
  ]);

  const [editingId, setEditingId] = useState<string | null>(null);
  const [newKeys, setNewKeys] = useState<string>('');

  const handleEdit = (id: string, currentKeys: string[]) => {
    setEditingId(id);
    setNewKeys(currentKeys.join(' + '));
  };

  const handleSave = (id: string) => {
    const keys = newKeys.split(' + ').map(k => k.trim()).filter(k => k);
    
    if (keys.length === 0) {
      toast({
        title: "Invalid Hotkey",
        description: "Please enter at least one key.",
        variant: "destructive",
      });
      return;
    }

    setHotkeys(prev => 
      prev.map(h => h.id === id ? { ...h, keys } : h)
    );
    
    setEditingId(null);
    setNewKeys('');
    
    toast({
      title: "Hotkey Updated",
      description: "Your hotkey has been successfully updated.",
    });
  };

  const handleCancel = () => {
    setEditingId(null);
    setNewKeys('');
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Keyboard className="h-4 w-4" />
          Customize Hotkeys
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5" />
            Hotkey Customization
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {hotkeys.map((hotkey) => (
            <div
              key={hotkey.id}
              className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <div className="flex-1">
                <div className="font-medium">{hotkey.action}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {hotkey.description}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {editingId === hotkey.id ? (
                  <div className="flex items-center gap-2">
                    <Input
                      value={newKeys}
                      onChange={(e) => setNewKeys(e.target.value)}
                      placeholder="Ctrl + S"
                      className="w-32"
                    />
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => handleSave(hotkey.id)}
                    >
                      <Save className="h-4 w-4" />
                    </Button>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={handleCancel}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <div className="flex gap-1">
                      {hotkey.keys.map((key, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {key}
                        </Badge>
                      ))}
                    </div>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => handleEdit(hotkey.id, hotkey.keys)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-sm text-gray-600 dark:text-gray-400 mt-4">
          <p>Tip: Use format like "Ctrl + S" or "Ctrl + Shift + P" for combination keys.</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};
