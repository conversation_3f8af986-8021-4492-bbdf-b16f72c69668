
import React, { useState } from 'react';
import { ShoppingBag, Star, Download, Eye, Heart, Filter, Search, Upload, DollarSign, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';

interface Template {
  id: string;
  name: string;
  description: string;
  preview: string;
  price: number;
  category: 'dashboard' | 'landing' | 'ecommerce' | 'blog' | 'portfolio' | 'app';
  author: {
    name: string;
    avatar: string;
    verified: boolean;
  };
  rating: number;
  reviews: number;
  downloads: number;
  likes: number;
  isLiked: boolean;
  isPurchased: boolean;
  tags: string[];
  framework: 'React' | 'Vue' | 'Angular';
  createdAt: Date;
}

export const TemplateMarketplace = () => {
  const [templates, setTemplates] = useState<Template[]>([
    {
      id: '1',
      name: 'Modern Dashboard Pro',
      description: 'Professional admin dashboard with advanced analytics and data visualization',
      preview: 'https://via.placeholder.com/400x250/3B82F6/white?text=Dashboard',
      price: 49,
      category: 'dashboard',
      author: {
        name: 'UI Masters',
        avatar: '👨‍🎨',
        verified: true
      },
      rating: 4.9,
      reviews: 127,
      downloads: 3420,
      likes: 892,
      isLiked: false,
      isPurchased: false,
      tags: ['dashboard', 'analytics', 'charts', 'responsive'],
      framework: 'React',
      createdAt: new Date(Date.now() - 86400000)
    },
    {
      id: '2',
      name: 'Startup Landing Kit',
      description: 'Complete landing page kit for startups with multiple variants and components',
      preview: 'https://via.placeholder.com/400x250/10B981/white?text=Landing',
      price: 29,
      category: 'landing',
      author: {
        name: 'StartupUI',
        avatar: '🚀',
        verified: true
      },
      rating: 4.8,
      reviews: 89,
      downloads: 2150,
      likes: 567,
      isLiked: true,
      isPurchased: true,
      tags: ['landing', 'startup', 'hero', 'pricing'],
      framework: 'React',
      createdAt: new Date(Date.now() - 172800000)
    },
    {
      id: '3',
      name: 'E-commerce Store',
      description: 'Full-featured e-commerce template with shopping cart and checkout',
      preview: 'https://via.placeholder.com/400x250/F59E0B/white?text=Store',
      price: 79,
      category: 'ecommerce',
      author: {
        name: 'Commerce Co',
        avatar: '🛒',
        verified: true
      },
      rating: 4.7,
      reviews: 203,
      downloads: 1890,
      likes: 734,
      isLiked: false,
      isPurchased: false,
      tags: ['ecommerce', 'shopping', 'cart', 'payment'],
      framework: 'React',
      createdAt: new Date(Date.now() - 259200000)
    },
    {
      id: '4',
      name: 'Creative Portfolio',
      description: 'Stunning portfolio template for designers and creative professionals',
      preview: 'https://via.placeholder.com/400x250/8B5CF6/white?text=Portfolio',
      price: 35,
      category: 'portfolio',
      author: {
        name: 'Design Studio',
        avatar: '🎨',
        verified: false
      },
      rating: 4.6,
      reviews: 67,
      downloads: 1245,
      likes: 423,
      isLiked: true,
      isPurchased: false,
      tags: ['portfolio', 'creative', 'gallery', 'showcase'],
      framework: 'React',
      createdAt: new Date(Date.now() - 345600000)
    },
    {
      id: '5',
      name: 'Blog & Magazine',
      description: 'Modern blog template with CMS integration and SEO optimization',
      preview: 'https://via.placeholder.com/400x250/EF4444/white?text=Blog',
      price: 39,
      category: 'blog',
      author: {
        name: 'Content Pro',
        avatar: '✍️',
        verified: true
      },
      rating: 4.5,
      reviews: 134,
      downloads: 2890,
      likes: 672,
      isLiked: false,
      isPurchased: false,
      tags: ['blog', 'cms', 'seo', 'content'],
      framework: 'React',
      createdAt: new Date(Date.now() - 432000000)
    },
    {
      id: '6',
      name: 'Mobile App UI Kit',
      description: 'Complete mobile app interface kit with 50+ screens',
      preview: 'https://via.placeholder.com/400x250/06B6D4/white?text=Mobile',
      price: 59,
      category: 'app',
      author: {
        name: 'Mobile First',
        avatar: '📱',
        verified: true
      },
      rating: 4.8,
      reviews: 98,
      downloads: 1567,
      likes: 445,
      isLiked: false,
      isPurchased: false,
      tags: ['mobile', 'app', 'ui-kit', 'components'],
      framework: 'React',
      createdAt: new Date(Date.now() - 518400000)
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | Template['category']>('all');
  const [priceFilter, setPriceFilter] = useState<'all' | 'free' | 'paid'>('all');
  const [sortBy, setSortBy] = useState<'newest' | 'popular' | 'rating' | 'price'>('newest');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [showUploadDialog, setShowUploadDialog] = useState(false);

  const categories = [
    { id: 'all', name: 'All Templates', count: templates.length },
    { id: 'dashboard', name: 'Dashboard', count: templates.filter(t => t.category === 'dashboard').length },
    { id: 'landing', name: 'Landing Pages', count: templates.filter(t => t.category === 'landing').length },
    { id: 'ecommerce', name: 'E-commerce', count: templates.filter(t => t.category === 'ecommerce').length },
    { id: 'blog', name: 'Blog', count: templates.filter(t => t.category === 'blog').length },
    { id: 'portfolio', name: 'Portfolio', count: templates.filter(t => t.category === 'portfolio').length },
    { id: 'app', name: 'Mobile App', count: templates.filter(t => t.category === 'app').length }
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesPrice = priceFilter === 'all' || 
                        (priceFilter === 'free' && template.price === 0) ||
                        (priceFilter === 'paid' && template.price > 0);
    return matchesSearch && matchesCategory && matchesPrice;
  });

  const sortedTemplates = [...filteredTemplates].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.downloads - a.downloads;
      case 'rating':
        return b.rating - a.rating;
      case 'price':
        return a.price - b.price;
      default:
        return b.createdAt.getTime() - a.createdAt.getTime();
    }
  });

  const toggleLike = (templateId: string) => {
    setTemplates(prev => prev.map(template =>
      template.id === templateId 
        ? { 
            ...template, 
            isLiked: !template.isLiked,
            likes: template.isLiked ? template.likes - 1 : template.likes + 1
          } 
        : template
    ));
  };

  const purchaseTemplate = (templateId: string) => {
    setTemplates(prev => prev.map(template =>
      template.id === templateId 
        ? { ...template, isPurchased: true, downloads: template.downloads + 1 }
        : template
    ));
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'dashboard': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'landing': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'ecommerce': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'blog': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'portfolio': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'app': return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold mb-2">Template Marketplace</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Discover and purchase premium templates from top designers
              </p>
            </div>
            <Button onClick={() => setShowUploadDialog(true)} className="bg-green-600 hover:bg-green-700">
              <Upload className="w-4 h-4 mr-2" />
              Sell Template
            </Button>
          </div>
          
          {/* Search and Filters */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="w-4 h-4" />
              </Button>
            </div>
            
            <div className="flex gap-2">
              <select
                value={priceFilter}
                onChange={(e) => setPriceFilter(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600"
              >
                <option value="all">All Prices</option>
                <option value="free">Free</option>
                <option value="paid">Paid</option>
              </select>
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600"
              >
                <option value="newest">Newest</option>
                <option value="popular">Most Popular</option>
                <option value="rating">Highest Rated</option>
                <option value="price">Price: Low to High</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Categories Sidebar */}
        <div className="w-64 bg-white dark:bg-gray-800 border-r p-4">
          <h2 className="font-semibold mb-4">Categories</h2>
          <div className="space-y-1">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id as any)}
                className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <span>{category.name}</span>
                <Badge variant="secondary">{category.count}</Badge>
              </button>
            ))}
          </div>

          <div className="mt-8">
            <h3 className="font-medium mb-4">Top Sellers</h3>
            <div className="space-y-3">
              {templates.slice(0, 3).map((template) => (
                <div key={template.id} className="flex items-center gap-2">
                  <Avatar className="w-8 h-8">
                    {template.author.avatar}
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{template.author.name}</p>
                    <p className="text-xs text-gray-500">{template.downloads} sales</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="max-w-6xl mx-auto">
            {/* Template Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {sortedTemplates.map((template) => (
                <div key={template.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  {/* Preview Image */}
                  <div className="relative group">
                    <img 
                      src={template.preview} 
                      alt={template.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button
                        variant="secondary"
                        onClick={() => setSelectedTemplate(template)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        Preview
                      </Button>
                    </div>
                    <div className="absolute top-2 right-2 flex gap-1">
                      <Button
                        size="icon"
                        variant="secondary"
                        className="w-8 h-8"
                        onClick={() => toggleLike(template.id)}
                      >
                        <Heart className={`w-4 h-4 ${template.isLiked ? 'fill-red-500 text-red-500' : ''}`} />
                      </Button>
                    </div>
                    <div className="absolute top-2 left-2">
                      <Badge className={getCategoryColor(template.category)}>
                        {template.category}
                      </Badge>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-lg truncate">{template.name}</h3>
                      <div className="text-lg font-bold text-green-600">
                        {template.price === 0 ? 'Free' : `$${template.price}`}
                      </div>
                    </div>

                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                      {template.description}
                    </p>

                    {/* Author */}
                    <div className="flex items-center gap-2 mb-3">
                      <Avatar className="w-6 h-6">
                        {template.author.avatar}
                      </Avatar>
                      <span className="text-sm font-medium">{template.author.name}</span>
                      {template.author.verified && (
                        <Badge variant="secondary" className="text-xs">Verified</Badge>
                      )}
                    </div>

                    {/* Stats */}
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        {template.rating}
                      </div>
                      <div className="flex items-center gap-1">
                        <Download className="w-4 h-4" />
                        {template.downloads.toLocaleString()}
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        {template.likes}
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1 mb-4">
                      {template.tags.slice(0, 3).map((tag, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      {template.isPurchased ? (
                        <Button className="flex-1" variant="outline">
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </Button>
                      ) : (
                        <Button 
                          className="flex-1" 
                          onClick={() => purchaseTemplate(template.id)}
                        >
                          <ShoppingBag className="w-4 h-4 mr-2" />
                          {template.price === 0 ? 'Get Free' : `Buy $${template.price}`}
                        </Button>
                      )}
                      <Button variant="outline" size="icon">
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Empty State */}
            {sortedTemplates.length === 0 && (
              <div className="text-center py-12">
                <ShoppingBag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No templates found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Try adjusting your search terms or filters
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Template Preview Modal */}
      {selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold">{selectedTemplate.name}</h3>
                <Button
                  variant="outline"
                  onClick={() => setSelectedTemplate(null)}
                >
                  Close
                </Button>
              </div>

              <img 
                src={selectedTemplate.preview} 
                alt={selectedTemplate.name}
                className="w-full h-64 object-cover rounded-lg mb-4"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">{selectedTemplate.description}</p>

                  <h4 className="font-medium mb-2">Features</h4>
                  <div className="flex flex-wrap gap-1 mb-4">
                    {selectedTemplate.tags.map((tag, idx) => (
                      <Badge key={idx} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Template Info</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Price:</span>
                      <span className="font-medium">
                        {selectedTemplate.price === 0 ? 'Free' : `$${selectedTemplate.price}`}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Framework:</span>
                      <span className="font-medium">{selectedTemplate.framework}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Downloads:</span>
                      <span className="font-medium">{selectedTemplate.downloads.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Rating:</span>
                      <span className="font-medium">{selectedTemplate.rating}/5</span>
                    </div>
                  </div>

                  <div className="mt-6">
                    {selectedTemplate.isPurchased ? (
                      <Button className="w-full">
                        <Download className="w-4 h-4 mr-2" />
                        Download Template
                      </Button>
                    ) : (
                      <Button 
                        className="w-full" 
                        onClick={() => {
                          purchaseTemplate(selectedTemplate.id);
                          setSelectedTemplate(null);
                        }}
                      >
                        <ShoppingBag className="w-4 h-4 mr-2" />
                        {selectedTemplate.price === 0 ? 'Get Free Template' : `Purchase for $${selectedTemplate.price}`}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Upload Template Dialog */}
      {showUploadDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96">
            <h3 className="text-lg font-semibold mb-4">Sell Your Template</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Join our marketplace and earn money by selling your premium templates to developers worldwide.
            </p>
            <div className="space-y-3 mb-4">
              <div className="flex items-center gap-2 text-sm">
                <DollarSign className="w-4 h-4 text-green-500" />
                <span>Earn up to 70% commission</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <TrendingUp className="w-4 h-4 text-blue-500" />
                <span>Reach thousands of developers</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Star className="w-4 h-4 text-yellow-500" />
                <span>Build your reputation</span>
              </div>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                className="flex-1"
                onClick={() => setShowUploadDialog(false)}
              >
                Cancel
              </Button>
              <Button className="flex-1">
                Start Selling
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
