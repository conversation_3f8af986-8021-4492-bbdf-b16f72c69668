
import React, { useState, useEffect } from 'react';
import { Palette, Type, Ruler, Save, Download, Upload, Eye } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface ColorToken {
  name: string;
  value: string;
  description?: string;
}

interface FontToken {
  name: string;
  family: string;
  size: string;
  weight: string;
}

interface SpacingToken {
  name: string;
  value: string;
}

export const DesignSystem = () => {
  const [colors, setColors] = useState<ColorToken[]>([
    { name: 'primary', value: '#3B82F6', description: 'Primary brand color' },
    { name: 'secondary', value: '#6B7280', description: 'Secondary color' },
    { name: 'accent', value: '#10B981', description: 'Accent color' },
    { name: 'danger', value: '#EF4444', description: 'Error/danger color' }
  ]);

  const [fonts, setFonts] = useState<FontToken[]>([
    { name: 'heading-xl', family: 'Inter', size: '2.25rem', weight: '700' },
    { name: 'heading-lg', family: 'Inter', size: '1.875rem', weight: '600' },
    { name: 'body', family: 'Inter', size: '1rem', weight: '400' },
    { name: 'caption', family: 'Inter', size: '0.875rem', weight: '400' }
  ]);

  const [spacing, setSpacing] = useState<SpacingToken[]>([
    { name: 'xs', value: '0.25rem' },
    { name: 'sm', value: '0.5rem' },
    { name: 'md', value: '1rem' },
    { name: 'lg', value: '1.5rem' },
    { name: 'xl', value: '2rem' },
    { name: '2xl', value: '3rem' }
  ]);

  const [newColor, setNewColor] = useState({ name: '', value: '#000000', description: '' });
  const [newFont, setNewFont] = useState({ name: '', family: 'Inter', size: '1rem', weight: '400' });
  const [newSpacing, setNewSpacing] = useState({ name: '', value: '1rem' });

  const addColor = () => {
    if (newColor.name && newColor.value) {
      setColors([...colors, { ...newColor }]);
      setNewColor({ name: '', value: '#000000', description: '' });
    }
  };

  const addFont = () => {
    if (newFont.name && newFont.family) {
      setFonts([...fonts, { ...newFont }]);
      setNewFont({ name: '', family: 'Inter', size: '1rem', weight: '400' });
    }
  };

  const addSpacing = () => {
    if (newSpacing.name && newSpacing.value) {
      setSpacing([...spacing, { ...newSpacing }]);
      setNewSpacing({ name: '', value: '1rem' });
    }
  };

  const generateCSS = () => {
    const colorVars = colors.map(color => `  --color-${color.name}: ${color.value};`).join('\n');
    const fontVars = fonts.map(font => `  --font-${font.name}: ${font.weight} ${font.size} ${font.family};`).join('\n');
    const spacingVars = spacing.map(space => `  --spacing-${space.name}: ${space.value};`).join('\n');
    
    return `:root {\n${colorVars}\n${fontVars}\n${spacingVars}\n}`;
  };

  const exportDesignSystem = () => {
    const designSystem = { colors, fonts, spacing };
    const blob = new Blob([JSON.stringify(designSystem, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'design-system.json';
    a.click();
  };

  return (
    <div className="h-screen flex flex-col">
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Design System</h2>
            <p className="text-gray-400">Manage colors, typography, and spacing tokens</p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={exportDesignSystem}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
            >
              <Download size={16} />
              Export
            </button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        <div className="w-1/2 p-6 overflow-y-auto">
          <Tabs defaultValue="colors" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="colors">Colors</TabsTrigger>
              <TabsTrigger value="typography">Typography</TabsTrigger>
              <TabsTrigger value="spacing">Spacing</TabsTrigger>
            </TabsList>

            <TabsContent value="colors" className="space-y-4">
              <div className="bg-gray-800 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">Add New Color</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="color-name">Name</Label>
                    <Input
                      id="color-name"
                      value={newColor.name}
                      onChange={(e) => setNewColor({...newColor, name: e.target.value})}
                      placeholder="primary"
                    />
                  </div>
                  <div>
                    <Label htmlFor="color-value">Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="color-value"
                        type="color"
                        value={newColor.value}
                        onChange={(e) => setNewColor({...newColor, value: e.target.value})}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={newColor.value}
                        onChange={(e) => setNewColor({...newColor, value: e.target.value})}
                        placeholder="#000000"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="color-desc">Description</Label>
                    <Input
                      id="color-desc"
                      value={newColor.description}
                      onChange={(e) => setNewColor({...newColor, description: e.target.value})}
                      placeholder="Primary brand color"
                    />
                  </div>
                </div>
                <button
                  onClick={addColor}
                  className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                >
                  Add Color
                </button>
              </div>

              <div className="space-y-2">
                {colors.map((color, index) => (
                  <div key={index} className="bg-gray-800 p-4 rounded-lg flex items-center gap-4">
                    <div
                      className="w-12 h-12 rounded-lg border border-gray-600"
                      style={{ backgroundColor: color.value }}
                    />
                    <div className="flex-1">
                      <h4 className="font-medium">{color.name}</h4>
                      <p className="text-sm text-gray-400">{color.value}</p>
                      {color.description && (
                        <p className="text-xs text-gray-500">{color.description}</p>
                      )}
                    </div>
                    <button
                      onClick={() => navigator.clipboard.writeText(color.value)}
                      className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm transition-colors"
                    >
                      Copy
                    </button>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="typography" className="space-y-4">
              <div className="bg-gray-800 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">Add New Font Token</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="font-name">Name</Label>
                    <Input
                      id="font-name"
                      value={newFont.name}
                      onChange={(e) => setNewFont({...newFont, name: e.target.value})}
                      placeholder="heading-xl"
                    />
                  </div>
                  <div>
                    <Label htmlFor="font-family">Font Family</Label>
                    <Input
                      id="font-family"
                      value={newFont.family}
                      onChange={(e) => setNewFont({...newFont, family: e.target.value})}
                      placeholder="Inter"
                    />
                  </div>
                  <div>
                    <Label htmlFor="font-size">Size</Label>
                    <Input
                      id="font-size"
                      value={newFont.size}
                      onChange={(e) => setNewFont({...newFont, size: e.target.value})}
                      placeholder="1rem"
                    />
                  </div>
                  <div>
                    <Label htmlFor="font-weight">Weight</Label>
                    <Input
                      id="font-weight"
                      value={newFont.weight}
                      onChange={(e) => setNewFont({...newFont, weight: e.target.value})}
                      placeholder="400"
                    />
                  </div>
                </div>
                <button
                  onClick={addFont}
                  className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                >
                  Add Font
                </button>
              </div>

              <div className="space-y-2">
                {fonts.map((font, index) => (
                  <div key={index} className="bg-gray-800 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{font.name}</h4>
                      <button
                        onClick={() => navigator.clipboard.writeText(`font: var(--font-${font.name})`)}
                        className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm transition-colors"
                      >
                        Copy
                      </button>
                    </div>
                    <div
                      className="text-white mb-2"
                      style={{
                        fontFamily: font.family,
                        fontSize: font.size,
                        fontWeight: font.weight
                      }}
                    >
                      The quick brown fox jumps over the lazy dog
                    </div>
                    <p className="text-sm text-gray-400">
                      {font.weight} {font.size} {font.family}
                    </p>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="spacing" className="space-y-4">
              <div className="bg-gray-800 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">Add New Spacing Token</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="spacing-name">Name</Label>
                    <Input
                      id="spacing-name"
                      value={newSpacing.name}
                      onChange={(e) => setNewSpacing({...newSpacing, name: e.target.value})}
                      placeholder="lg"
                    />
                  </div>
                  <div>
                    <Label htmlFor="spacing-value">Value</Label>
                    <Input
                      id="spacing-value"
                      value={newSpacing.value}
                      onChange={(e) => setNewSpacing({...newSpacing, value: e.target.value})}
                      placeholder="1rem"
                    />
                  </div>
                </div>
                <button
                  onClick={addSpacing}
                  className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                >
                  Add Spacing
                </button>
              </div>

              <div className="space-y-2">
                {spacing.map((space, index) => (
                  <div key={index} className="bg-gray-800 p-4 rounded-lg flex items-center gap-4">
                    <div
                      className="bg-blue-500 rounded"
                      style={{ width: space.value, height: space.value, minWidth: '8px', minHeight: '8px' }}
                    />
                    <div className="flex-1">
                      <h4 className="font-medium">{space.name}</h4>
                      <p className="text-sm text-gray-400">{space.value}</p>
                    </div>
                    <button
                      onClick={() => navigator.clipboard.writeText(space.value)}
                      className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm transition-colors"
                    >
                      Copy
                    </button>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="w-1/2 bg-gray-800 border-l border-gray-700 p-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">Generated CSS Variables</h3>
            <p className="text-sm text-gray-400 mb-4">Copy this CSS to use your design tokens</p>
          </div>
          
          <div className="bg-gray-900 p-4 rounded-lg overflow-y-auto h-full">
            <pre className="text-green-400 text-sm">
              <code>{generateCSS()}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};
