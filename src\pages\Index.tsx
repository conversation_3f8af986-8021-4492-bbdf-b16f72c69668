
import React, { useState } from 'react';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';
import { Dashboard } from '@/components/Dashboard';
import { CodeEditor } from '@/components/CodeEditor';
import { ComponentLibrary } from '@/components/ComponentLibrary';
import { FileGenerator } from '@/components/FileGenerator';
import { TaskTracker } from '@/components/TaskTracker';
import { DesignSystem } from '@/components/DesignSystem';
import { SnippetManager } from '@/components/SnippetManager';
import { AIAssistant } from '@/components/AIAssistant';
import { APIMocking } from '@/components/APIMocking';
import { Collaboration } from '@/components/Collaboration';
import { ProjectVersioning } from '@/components/ProjectVersioning';
import { DragDropBuilder } from '@/components/DragDropBuilder';
import { MultiProjectManager } from '@/components/MultiProjectManager';
import { PluginSystem } from '@/components/PluginSystem';
import { TemplateMarketplace } from '@/components/TemplateMarketplace';

type WorkspaceView = 
  | 'dashboard' 
  | 'design-system' 
  | 'components' 
  | 'snippets' 
  | 'ai-assistant' 
  | 'api-mocking' 
  | 'editor' 
  | 'generator' 
  | 'tasks'
  | 'collaboration'
  | 'versioning'
  | 'drag-drop'
  | 'projects'
  | 'plugins'
  | 'marketplace';

const Index = () => {
  const [activeView, setActiveView] = useState<WorkspaceView>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const renderActiveView = () => {
    switch (activeView) {
      case 'dashboard':
        return <Dashboard />;
      case 'design-system':
        return <DesignSystem />;
      case 'components':
        return <ComponentLibrary />;
      case 'snippets':
        return <SnippetManager />;
      case 'ai-assistant':
        return <AIAssistant />;
      case 'api-mocking':
        return <APIMocking />;
      case 'editor':
        return <CodeEditor />;
      case 'generator':
        return <FileGenerator />;
      case 'tasks':
        return <TaskTracker />;
      case 'collaboration':
        return <Collaboration />;
      case 'versioning':
        return <ProjectVersioning />;
      case 'drag-drop':
        return <DragDropBuilder />;
      case 'projects':
        return <MultiProjectManager />;
      case 'plugins':
        return <PluginSystem />;
      case 'marketplace':
        return <TemplateMarketplace />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white flex flex-col">
      <Header />
      <div className="flex flex-1">
        <Sidebar 
          activeView={activeView} 
          setActiveView={setActiveView}
          isOpen={sidebarOpen}
          onToggle={() => setSidebarOpen(!sidebarOpen)}
        />
        <main className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
          {renderActiveView()}
        </main>
      </div>
    </div>
  );
};

export default Index;
