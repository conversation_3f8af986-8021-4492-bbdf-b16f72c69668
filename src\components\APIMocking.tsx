
import React, { useState } from 'react';
import { Server, Plus, Edit, Trash2, Play, Download, Upload, Copy } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';

interface APIEndpoint {
  id: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  path: string;
  response: string;
  status: number;
  delay?: number;
  description: string;
}

interface MockServer {
  id: string;
  name: string;
  baseUrl: string;
  endpoints: APIEndpoint[];
  isRunning: boolean;
}

export const APIMocking = () => {
  const [servers, setServers] = useState<MockServer[]>([
    {
      id: '1',
      name: 'Default Server',
      baseUrl: 'http://localhost:3001',
      isRunning: false,
      endpoints: [
        {
          id: '1',
          method: 'GET',
          path: '/api/users',
          response: JSON.stringify([
            { id: 1, name: '<PERSON>', email: '<EMAIL>' },
            { id: 2, name: '<PERSON>', email: '<EMAIL>' }
          ], null, 2),
          status: 200,
          delay: 0,
          description: 'Get all users'
        }
      ]
    }
  ]);

  const [selectedServer, setSelectedServer] = useState<string>('1');
  const [selectedEndpoint, setSelectedEndpoint] = useState<string | null>(null);
  const [isEditingEndpoint, setIsEditingEndpoint] = useState(false);
  const [endpointForm, setEndpointForm] = useState<Partial<APIEndpoint>>({});

  const currentServer = servers.find(s => s.id === selectedServer);
  const currentEndpoint = currentServer?.endpoints.find(e => e.id === selectedEndpoint);

  const addNewEndpoint = () => {
    setEndpointForm({
      method: 'GET',
      path: '/api/new-endpoint',
      response: '{"message": "Hello World"}',
      status: 200,
      delay: 0,
      description: 'New endpoint'
    });
    setSelectedEndpoint(null);
    setIsEditingEndpoint(true);
  };

  const editEndpoint = (endpoint: APIEndpoint) => {
    setEndpointForm(endpoint);
    setSelectedEndpoint(endpoint.id);
    setIsEditingEndpoint(true);
  };

  const saveEndpoint = () => {
    if (!currentServer || !endpointForm.path) return;

    const newEndpoint: APIEndpoint = {
      id: selectedEndpoint || Date.now().toString(),
      method: endpointForm.method || 'GET',
      path: endpointForm.path,
      response: endpointForm.response || '{}',
      status: endpointForm.status || 200,
      delay: endpointForm.delay || 0,
      description: endpointForm.description || ''
    };

    setServers(prev => prev.map(server => {
      if (server.id === selectedServer) {
        const endpoints = selectedEndpoint
          ? server.endpoints.map(e => e.id === selectedEndpoint ? newEndpoint : e)
          : [...server.endpoints, newEndpoint];
        return { ...server, endpoints };
      }
      return server;
    }));

    setIsEditingEndpoint(false);
    setSelectedEndpoint(newEndpoint.id);
    setEndpointForm({});
  };

  const deleteEndpoint = (endpointId: string) => {
    setServers(prev => prev.map(server => {
      if (server.id === selectedServer) {
        return {
          ...server,
          endpoints: server.endpoints.filter(e => e.id !== endpointId)
        };
      }
      return server;
    }));
    setSelectedEndpoint(null);
  };

  const toggleServer = (serverId: string) => {
    setServers(prev => prev.map(server => {
      if (server.id === serverId) {
        console.log(`${server.isRunning ? 'Stopping' : 'Starting'} mock server: ${server.name}`);
        return { ...server, isRunning: !server.isRunning };
      }
      return server;
    }));
  };

  const exportServerConfig = () => {
    if (!currentServer) return;
    
    const config = {
      name: currentServer.name,
      baseUrl: currentServer.baseUrl,
      endpoints: currentServer.endpoints
    };
    
    const element = document.createElement('a');
    const file = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    element.href = URL.createObjectURL(file);
    element.download = `${currentServer.name.replace(/\s+/g, '_').toLowerCase()}_mock_config.json`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const generateCurl = (endpoint: APIEndpoint) => {
    if (!currentServer) return '';
    
    let curl = `curl -X ${endpoint.method} "${currentServer.baseUrl}${endpoint.path}"`;
    
    if (endpoint.method !== 'GET') {
      curl += ` -H "Content-Type: application/json"`;
      if (endpoint.method === 'POST' || endpoint.method === 'PUT') {
        curl += ` -d '${endpoint.response}'`;
      }
    }
    
    return curl;
  };

  return (
    <div className="h-screen flex">
      <div className="w-1/4 bg-gray-800 border-r border-gray-700 overflow-y-auto">
        <div className="p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold mb-2">API Mocking</h2>
          <p className="text-gray-400 text-sm">Create and manage mock APIs for frontend development</p>
        </div>

        <div className="p-4">
          <div className="mb-4">
            <h3 className="font-medium mb-2">Mock Servers</h3>
            {servers.map((server) => (
              <div
                key={server.id}
                onClick={() => setSelectedServer(server.id)}
                className={`p-3 rounded-lg cursor-pointer transition-colors mb-2 ${
                  selectedServer === server.id
                    ? 'bg-blue-600'
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-sm">{server.name}</h4>
                    <p className="text-xs text-gray-400">{server.baseUrl}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${server.isRunning ? 'bg-green-400' : 'bg-gray-500'}`}></div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleServer(server.id);
                      }}
                      className={`p-1 rounded transition-colors ${
                        server.isRunning ? 'text-red-400 hover:bg-red-900' : 'text-green-400 hover:bg-green-900'
                      }`}
                    >
                      <Play size={12} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {currentServer && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium">Endpoints</h3>
                <button
                  onClick={addNewEndpoint}
                  className="p-1 text-blue-400 hover:bg-blue-900 rounded transition-colors"
                >
                  <Plus size={16} />
                </button>
              </div>
              
              <div className="space-y-1">
                {currentServer.endpoints.map((endpoint) => (
                  <div
                    key={endpoint.id}
                    onClick={() => setSelectedEndpoint(endpoint.id)}
                    className={`p-2 rounded cursor-pointer transition-colors ${
                      selectedEndpoint === endpoint.id
                        ? 'bg-blue-600'
                        : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <span className={`text-xs px-2 py-1 rounded mr-2 ${
                          endpoint.method === 'GET' ? 'bg-green-600' :
                          endpoint.method === 'POST' ? 'bg-blue-600' :
                          endpoint.method === 'PUT' ? 'bg-yellow-600' :
                          'bg-red-600'
                        }`}>
                          {endpoint.method}
                        </span>
                        <span className="text-sm">{endpoint.path}</span>
                      </div>
                      <div className="flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            editEndpoint(endpoint);
                          }}
                          className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded"
                        >
                          <Edit size={12} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteEndpoint(endpoint.id);
                          }}
                          className="p-1 text-red-400 hover:bg-red-900 rounded"
                        >
                          <Trash2 size={12} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        {currentServer && (
          <>
            <div className="bg-gray-800 border-b border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Server className="w-6 h-6 text-blue-400" />
                  <div>
                    <h3 className="text-xl font-semibold">{currentServer.name}</h3>
                    <p className="text-gray-400 text-sm">{currentServer.baseUrl}</p>
                  </div>
                  <div className={`px-2 py-1 rounded text-xs ${
                    currentServer.isRunning ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'
                  }`}>
                    {currentServer.isRunning ? 'Running' : 'Stopped'}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={exportServerConfig}
                    className="flex items-center gap-2 px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded transition-colors text-sm"
                  >
                    <Download size={14} />
                    Export
                  </button>
                  <button
                    onClick={() => toggleServer(currentServer.id)}
                    className={`flex items-center gap-2 px-4 py-2 rounded transition-colors ${
                      currentServer.isRunning
                        ? 'bg-red-600 hover:bg-red-700'
                        : 'bg-green-600 hover:bg-green-700'
                    }`}
                  >
                    <Play size={16} />
                    {currentServer.isRunning ? 'Stop' : 'Start'}
                  </button>
                </div>
              </div>
            </div>

            <div className="flex-1 p-6">
              {isEditingEndpoint ? (
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold">
                    {selectedEndpoint ? 'Edit' : 'Add'} Endpoint
                  </h4>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Method</label>
                      <select
                        value={endpointForm.method || 'GET'}
                        onChange={(e) => setEndpointForm(prev => ({ ...prev, method: e.target.value as any }))}
                        className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-400 outline-none"
                      >
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">Path</label>
                      <input
                        type="text"
                        value={endpointForm.path || ''}
                        onChange={(e) => setEndpointForm(prev => ({ ...prev, path: e.target.value }))}
                        placeholder="/api/endpoint"
                        className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-400 outline-none"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Description</label>
                    <input
                      type="text"
                      value={endpointForm.description || ''}
                      onChange={(e) => setEndpointForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Endpoint description"
                      className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-400 outline-none"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Status Code</label>
                      <input
                        type="number"
                        value={endpointForm.status || 200}
                        onChange={(e) => setEndpointForm(prev => ({ ...prev, status: parseInt(e.target.value) }))}
                        className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-400 outline-none"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">Delay (ms)</label>
                      <input
                        type="number"
                        value={endpointForm.delay || 0}
                        onChange={(e) => setEndpointForm(prev => ({ ...prev, delay: parseInt(e.target.value) }))}
                        className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-400 outline-none"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Response JSON</label>
                    <Textarea
                      value={endpointForm.response || ''}
                      onChange={(e) => setEndpointForm(prev => ({ ...prev, response: e.target.value }))}
                      placeholder='{"message": "Hello World"}'
                      className="w-full bg-gray-700 border-gray-600 text-white font-mono"
                      rows={10}
                    />
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={saveEndpoint}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                    >
                      Save Endpoint
                    </button>
                    <button
                      onClick={() => {
                        setIsEditingEndpoint(false);
                        setEndpointForm({});
                      }}
                      className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : currentEndpoint ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <span className={`text-xs px-2 py-1 rounded ${
                          currentEndpoint.method === 'GET' ? 'bg-green-600' :
                          currentEndpoint.method === 'POST' ? 'bg-blue-600' :
                          currentEndpoint.method === 'PUT' ? 'bg-yellow-600' :
                          'bg-red-600'
                        }`}>
                          {currentEndpoint.method}
                        </span>
                        <h3 className="text-lg font-semibold">{currentEndpoint.path}</h3>
                      </div>
                      <p className="text-gray-400">{currentEndpoint.description}</p>
                    </div>
                    
                    <button
                      onClick={() => editEndpoint(currentEndpoint)}
                      className="flex items-center gap-2 px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                    >
                      <Edit size={14} />
                      Edit
                    </button>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-800 p-4 rounded-lg">
                      <h4 className="font-medium mb-2">Endpoint Details</h4>
                      <div className="space-y-2 text-sm">
                        <div><span className="text-gray-400">URL:</span> {currentServer.baseUrl}{currentEndpoint.path}</div>
                        <div><span className="text-gray-400">Status:</span> {currentEndpoint.status}</div>
                        <div><span className="text-gray-400">Delay:</span> {currentEndpoint.delay}ms</div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-800 p-4 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">cURL Command</h4>
                        <button
                          onClick={() => navigator.clipboard.writeText(generateCurl(currentEndpoint))}
                          className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded"
                        >
                          <Copy size={14} />
                        </button>
                      </div>
                      <pre className="text-xs bg-gray-900 p-2 rounded overflow-auto">
                        {generateCurl(currentEndpoint)}
                      </pre>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Response Preview</h4>
                    <pre className="bg-gray-900 p-4 rounded-lg overflow-auto text-sm">
                      {JSON.stringify(JSON.parse(currentEndpoint.response || '{}'), null, 2)}
                    </pre>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <Server className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Select an Endpoint</h3>
                    <p className="text-gray-400">Choose an endpoint to view details or create a new one</p>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};
