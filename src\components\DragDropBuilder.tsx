import React, { useState, useRef, useCallback } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { Layers, Square, Type, Image, Grid, Smartphone, Monitor, Tablet, Eye, Code, Save, List, MessageSquare, AlertCircle, CheckCircle, XCircle, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface Element {
  id: string;
  type: 'container' | 'text' | 'button' | 'image' | 'input' | 'card' | 'heading' | 'paragraph' | 'list' | 'listItem' | 'alert' | 'form' | 'textarea';
  props: Record<string, any>;
  children?: Element[];
}

interface ComponentTemplate {
  id: string;
  name: string;
  icon: React.ReactNode;
  element: Element;
}

export const DragDropBuilder = () => {
  const [elements, setElements] = useState<Element[]>([]);
  const [selectedElement, setSelectedElement] = useState<Element | null>(null);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [viewMode, setViewMode] = useState<'design' | 'preview' | 'code'>('design');
  
  const componentTemplates: ComponentTemplate[] = [
    {
      id: 'container',
      name: 'Container',
      icon: <Square className="w-4 h-4" />,
      element: {
        id: '',
        type: 'container',
        props: { 
          className: 'p-4 border-2 border-dashed border-gray-300 min-h-24 rounded-lg',
          style: { backgroundColor: 'transparent' }
        },
        children: []
      }
    },
    {
      id: 'heading',
      name: 'Heading',
      icon: <Type className="w-4 h-4" />,
      element: {
        id: '',
        type: 'heading',
        props: { 
          children: 'Heading Text',
          className: 'text-2xl font-bold mb-4'
        }
      }
    },
    {
      id: 'paragraph',
      name: 'Paragraph',
      icon: <Type className="w-4 h-4" />,
      element: {
        id: '',
        type: 'paragraph',
        props: { 
          children: 'This is a paragraph of text. You can edit this content.',
          className: 'text-gray-700 mb-4'
        }
      }
    },
    {
      id: 'button',
      name: 'Button',
      icon: <Square className="w-4 h-4" />,
      element: {
        id: '',
        type: 'button',
        props: { 
          children: 'Click Me',
          className: 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
        }
      }
    },
    {
      id: 'image',
      name: 'Image',
      icon: <Image className="w-4 h-4" />,
      element: {
        id: '',
        type: 'image',
        props: { 
          src: 'https://via.placeholder.com/300x200',
          alt: 'Placeholder',
          className: 'w-full h-48 object-cover rounded'
        }
      }
    },
    {
      id: 'input',
      name: 'Input',
      icon: <Type className="w-4 h-4" />,
      element: {
        id: '',
        type: 'input',
        props: { 
          placeholder: 'Enter text...',
          className: 'w-full p-2 border border-gray-300 rounded'
        }
      }
    },
    {
      id: 'textarea',
      name: 'Textarea',
      icon: <MessageSquare className="w-4 h-4" />,
      element: {
        id: '',
        type: 'textarea',
        props: { 
          placeholder: 'Enter your message...',
          className: 'w-full p-2 border border-gray-300 rounded',
          rows: 4
        }
      }
    },
    {
      id: 'list',
      name: 'List',
      icon: <List className="w-4 h-4" />,
      element: {
        id: '',
        type: 'list',
        props: {
          className: 'list-disc pl-6 space-y-2'
        },
        children: [
          {
            id: '',
            type: 'listItem',
            props: {
              children: 'First item',
              className: 'text-gray-700'
            }
          },
          {
            id: '',
            type: 'listItem',
            props: {
              children: 'Second item',
              className: 'text-gray-700'
            }
          }
        ]
      }
    },
    {
      id: 'alert',
      name: 'Alert',
      icon: <AlertCircle className="w-4 h-4" />,
      element: {
        id: '',
        type: 'alert',
        props: {
          type: 'info',
          message: 'This is an informational message',
          className: 'p-4 rounded-lg border bg-blue-900/50 text-blue-400 border-blue-500'
        }
      }
    },
    {
      id: 'form',
      name: 'Form',
      icon: <Grid className="w-4 h-4" />,
      element: {
        id: '',
        type: 'form',
        props: {
          className: 'space-y-4'
        },
        children: [
          {
            id: '',
            type: 'input',
            props: {
              placeholder: 'Name',
              className: 'w-full p-2 border border-gray-300 rounded'
            }
          },
          {
            id: '',
            type: 'input',
            props: {
              placeholder: 'Email',
              className: 'w-full p-2 border border-gray-300 rounded'
            }
          },
          {
            id: '',
            type: 'textarea',
            props: {
              placeholder: 'Message',
              className: 'w-full p-2 border border-gray-300 rounded',
              rows: 4
            }
          },
          {
            id: '',
            type: 'button',
            props: {
              children: 'Submit',
              className: 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
            }
          }
        ]
      }
    }
  ];

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const handleDragEnd = useCallback((result: DropResult) => {
    if (!result.destination) return;

    const { source, destination } = result;
    
    if (source.droppableId === 'components' && destination.droppableId === 'canvas') {
      // Adding new component from sidebar
      const template = componentTemplates.find(t => t.id === result.draggableId);
      if (template) {
        const newElement: Element = {
          ...template.element,
          id: generateId()
        };
        
        setElements(prev => [...prev, newElement]);
      }
    } else if (source.droppableId === 'canvas' && destination.droppableId === 'canvas') {
      // Reordering within canvas
      const newElements = Array.from(elements);
      const [removed] = newElements.splice(source.index, 1);
      newElements.splice(destination.index, 0, removed);
      setElements(newElements);
    }
  }, [elements]);

  const renderElement = (element: Element, isSelected: boolean = false): React.ReactNode => {
    const commonProps = {
      key: element.id,
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation();
        setSelectedElement(element);
      },
      className: `${element.props.className || ''} ${isSelected ? 'ring-2 ring-blue-500' : ''} cursor-pointer`,
      style: element.props.style
    };

    switch (element.type) {
      case 'container':
      case 'form':
        return (
          <div {...commonProps}>
            {element.children?.map(child => renderElement(child))}
            {(!element.children || element.children.length === 0) && (
              <div className="text-gray-400 text-center py-8">
                Drop components here
              </div>
            )}
          </div>
        );
      case 'heading':
        return <h2 {...commonProps}>{element.props.children}</h2>;
      case 'paragraph':
        return <p {...commonProps}>{element.props.children}</p>;
      case 'button':
        return <button {...commonProps}>{element.props.children}</button>;
      case 'image':
        return <img {...commonProps} src={element.props.src} alt={element.props.alt} />;
      case 'input':
        return <input {...commonProps} placeholder={element.props.placeholder} />;
      case 'textarea':
        return <textarea {...commonProps} placeholder={element.props.placeholder} rows={element.props.rows} />;
      case 'list':
        return (
          <ul {...commonProps}>
            {element.children?.map(child => renderElement(child))}
          </ul>
        );
      case 'listItem':
        return <li {...commonProps}>{element.props.children}</li>;
      case 'alert':
        const alertIcons = {
          success: <CheckCircle className="w-5 h-5 text-green-400" />,
          error: <XCircle className="w-5 h-5 text-red-400" />,
          warning: <AlertCircle className="w-5 h-5 text-yellow-400" />,
          info: <Info className="w-5 h-5 text-blue-400" />
        };
        return (
          <div {...commonProps}>
            <div className="flex items-center">
              {alertIcons[element.props.type as keyof typeof alertIcons]}
              <p className="ml-3">{element.props.message}</p>
            </div>
          </div>
        );
      default:
        return <div {...commonProps}>Unknown element</div>;
    }
  };

  const generateCode = (): string => {
    const elementsToCode = (elements: Element[]): string => {
      return elements.map(element => {
        const props = Object.entries(element.props)
          .filter(([key]) => key !== 'children' && key !== 'style')
          .map(([key, value]) => `${key}="${value}"`)
          .join(' ');
        
        const style = element.props.style 
          ? ` style={${JSON.stringify(element.props.style)}}`
          : '';

        switch (element.type) {
          case 'container':
            const children = element.children ? elementsToCode(element.children) : '';
            return `<div ${props}${style}>\n${children}\n</div>`;
          case 'text':
            return `<p ${props}${style}>${element.props.children}</p>`;
          case 'button':
            return `<button ${props}${style}>${element.props.children}</button>`;
          case 'image':
            return `<img ${props}${style} />`;
          case 'input':
            return `<input ${props}${style} />`;
          default:
            return `<div ${props}${style}>Unknown element</div>`;
        }
      }).join('\n');
    };

    return `import React from 'react';

const GeneratedComponent = () => {
  return (
    <div className="p-4">
${elementsToCode(elements).split('\n').map(line => `      ${line}`).join('\n')}
    </div>
  );
};

export default GeneratedComponent;`;
  };

  const getPreviewWidth = () => {
    switch (previewMode) {
      case 'mobile': return 'max-w-sm';
      case 'tablet': return 'max-w-2xl';
      default: return 'max-w-full';
    }
  };

  return (
    <div className="h-screen flex">
      <DragDropContext onDragEnd={handleDragEnd}>
        {/* Component Sidebar */}
        <div className="w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto">
          <div className="p-4 border-b border-gray-700">
            <h2 className="text-xl font-semibold text-white">Components</h2>
            <p className="text-gray-400 text-sm">Drag to add</p>
          </div>
          
          <Droppable droppableId="components" isDropDisabled={true}>
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="p-4 space-y-2"
              >
                {componentTemplates.map((template, index) => (
                  <Draggable
                    key={template.id}
                    draggableId={template.id}
                    index={index}
                  >
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className="flex items-center gap-2 p-3 bg-gray-700 rounded-lg cursor-move hover:bg-gray-600"
                      >
                        {template.icon}
                        <span className="text-white">{template.name}</span>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </div>

        {/* Main Canvas */}
        <div className="flex-1 flex flex-col">
          <div className="bg-gray-800 border-b border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setPreviewMode('desktop')}
                  className={`p-2 rounded ${previewMode === 'desktop' ? 'bg-blue-600' : 'bg-gray-700'}`}
                >
                  <Monitor className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setPreviewMode('tablet')}
                  className={`p-2 rounded ${previewMode === 'tablet' ? 'bg-blue-600' : 'bg-gray-700'}`}
                >
                  <Tablet className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setPreviewMode('mobile')}
                  className={`p-2 rounded ${previewMode === 'mobile' ? 'bg-blue-600' : 'bg-gray-700'}`}
                >
                  <Smartphone className="w-5 h-5" />
                </button>
              </div>
              
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setViewMode('design')}
                  className={`flex items-center gap-2 px-4 py-2 rounded ${
                    viewMode === 'design' ? 'bg-blue-600' : 'bg-gray-700'
                  }`}
                >
                  <Eye className="w-4 h-4" />
                  Design
                </button>
                <button
                  onClick={() => setViewMode('preview')}
                  className={`flex items-center gap-2 px-4 py-2 rounded ${
                    viewMode === 'preview' ? 'bg-blue-600' : 'bg-gray-700'
                  }`}
                >
                  <Eye className="w-4 h-4" />
                  Preview
                </button>
                <button
                  onClick={() => setViewMode('code')}
                  className={`flex items-center gap-2 px-4 py-2 rounded ${
                    viewMode === 'code' ? 'bg-blue-600' : 'bg-gray-700'
                  }`}
                >
                  <Code className="w-4 h-4" />
                  Code
                </button>
              </div>
            </div>
          </div>

          <div className="flex-1 p-8 overflow-auto">
            <div className={`mx-auto ${getPreviewWidth()}`}>
              {viewMode === 'code' ? (
                <pre className="bg-gray-900 p-4 rounded-lg overflow-auto">
                  <code className="text-green-400">{generateCode()}</code>
                </pre>
              ) : (
                <Droppable droppableId="canvas">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="min-h-[500px] bg-white rounded-lg shadow-lg p-8"
                    >
                      {elements.map((element, index) => (
                        <Draggable
                          key={element.id}
                          draggableId={element.id}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              {renderElement(element, selectedElement?.id === element.id)}
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              )}
            </div>
          </div>
        </div>
      </DragDropContext>
    </div>
  );
};
