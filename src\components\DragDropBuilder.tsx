import React, { useState, useRef, useCallback, useEffect } from 'react';
import { DragDropContext, Draggable, DropResult } from 'react-beautiful-dnd';
import { StrictModeDroppable } from './StrictModeDroppable';
import { Layers, Square, Type, Image, Grid, Smartphone, Monitor, Tablet, Eye, Code, Save, List, MessageSquare, AlertCircle, CheckCircle, XCircle, Info, Trash2, Copy } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface Element {
  id: string;
  type: 'container' | 'text' | 'button' | 'image' | 'input' | 'card' | 'heading' | 'paragraph' | 'list' | 'listItem' | 'alert' | 'form' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'badge' | 'divider';
  props: Record<string, any>;
  children?: Element[];
}

interface ComponentTemplate {
  id: string;
  name: string;
  icon: React.ReactNode;
  element: Element;
}

export const DragDropBuilder = () => {
  const [elements, setElements] = useState<Element[]>([]);
  const [selectedElement, setSelectedElement] = useState<Element | null>(null);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [viewMode, setViewMode] = useState<'design' | 'preview' | 'code'>('design');
  const [isDragDisabled, setIsDragDisabled] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  
  const componentTemplates: ComponentTemplate[] = [
    {
      id: 'container',
      name: 'Container',
      icon: <Square className="w-4 h-4" />,
      element: {
        id: '',
        type: 'container',
        props: { 
          className: 'p-4 border-2 border-dashed border-gray-300 min-h-24 rounded-lg',
          style: { backgroundColor: 'transparent' }
        },
        children: []
      }
    },
    {
      id: 'heading',
      name: 'Heading',
      icon: <Type className="w-4 h-4" />,
      element: {
        id: '',
        type: 'heading',
        props: { 
          children: 'Heading Text',
          className: 'text-2xl font-bold mb-4'
        }
      }
    },
    {
      id: 'paragraph',
      name: 'Paragraph',
      icon: <Type className="w-4 h-4" />,
      element: {
        id: '',
        type: 'paragraph',
        props: { 
          children: 'This is a paragraph of text. You can edit this content.',
          className: 'text-gray-700 mb-4'
        }
      }
    },
    {
      id: 'button',
      name: 'Button',
      icon: <Square className="w-4 h-4" />,
      element: {
        id: '',
        type: 'button',
        props: { 
          children: 'Click Me',
          className: 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
        }
      }
    },
    {
      id: 'image',
      name: 'Image',
      icon: <Image className="w-4 h-4" />,
      element: {
        id: '',
        type: 'image',
        props: { 
          src: 'https://via.placeholder.com/300x200',
          alt: 'Placeholder',
          className: 'w-full h-48 object-cover rounded'
        }
      }
    },
    {
      id: 'input',
      name: 'Input',
      icon: <Type className="w-4 h-4" />,
      element: {
        id: '',
        type: 'input',
        props: { 
          placeholder: 'Enter text...',
          className: 'w-full p-2 border border-gray-300 rounded'
        }
      }
    },
    {
      id: 'textarea',
      name: 'Textarea',
      icon: <MessageSquare className="w-4 h-4" />,
      element: {
        id: '',
        type: 'textarea',
        props: { 
          placeholder: 'Enter your message...',
          className: 'w-full p-2 border border-gray-300 rounded',
          rows: 4
        }
      }
    },
    {
      id: 'list',
      name: 'List',
      icon: <List className="w-4 h-4" />,
      element: {
        id: '',
        type: 'list',
        props: {
          className: 'list-disc pl-6 space-y-2'
        },
        children: [
          {
            id: '',
            type: 'listItem',
            props: {
              children: 'First item',
              className: 'text-gray-700'
            }
          },
          {
            id: '',
            type: 'listItem',
            props: {
              children: 'Second item',
              className: 'text-gray-700'
            }
          }
        ]
      }
    },
    {
      id: 'alert',
      name: 'Alert',
      icon: <AlertCircle className="w-4 h-4" />,
      element: {
        id: '',
        type: 'alert',
        props: {
          type: 'info',
          message: 'This is an informational message',
          className: 'p-4 rounded-lg border bg-blue-900/50 text-blue-400 border-blue-500'
        }
      }
    },
    {
      id: 'form',
      name: 'Form',
      icon: <Grid className="w-4 h-4" />,
      element: {
        id: '',
        type: 'form',
        props: {
          className: 'space-y-4'
        },
        children: [
          {
            id: '',
            type: 'input',
            props: {
              placeholder: 'Name',
              className: 'w-full p-2 border border-gray-300 rounded'
            }
          },
          {
            id: '',
            type: 'input',
            props: {
              placeholder: 'Email',
              className: 'w-full p-2 border border-gray-300 rounded'
            }
          },
          {
            id: '',
            type: 'textarea',
            props: {
              placeholder: 'Message',
              className: 'w-full p-2 border border-gray-300 rounded',
              rows: 4
            }
          },
          {
            id: '',
            type: 'button',
            props: {
              children: 'Submit',
              className: 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
            }
          }
        ]
      }
    },
    {
      id: 'select',
      name: 'Select',
      icon: <List className="w-4 h-4" />,
      element: {
        id: '',
        type: 'select',
        props: {
          className: 'w-full p-2 border border-gray-300 rounded',
          options: ['Option 1', 'Option 2', 'Option 3']
        }
      }
    },
    {
      id: 'checkbox',
      name: 'Checkbox',
      icon: <CheckCircle className="w-4 h-4" />,
      element: {
        id: '',
        type: 'checkbox',
        props: {
          label: 'Check this option',
          className: 'flex items-center space-x-2'
        }
      }
    },
    {
      id: 'radio',
      name: 'Radio',
      icon: <AlertCircle className="w-4 h-4" />,
      element: {
        id: '',
        type: 'radio',
        props: {
          name: 'radioGroup',
          options: ['Option 1', 'Option 2'],
          className: 'space-y-2'
        }
      }
    },
    {
      id: 'badge',
      name: 'Badge',
      icon: <Square className="w-4 h-4" />,
      element: {
        id: '',
        type: 'badge',
        props: {
          children: 'Badge',
          className: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'
        }
      }
    },
    {
      id: 'divider',
      name: 'Divider',
      icon: <Grid className="w-4 h-4" />,
      element: {
        id: '',
        type: 'divider',
        props: {
          className: 'border-t border-gray-300 my-4'
        }
      }
    },
    {
      id: 'card',
      name: 'Card',
      icon: <Square className="w-4 h-4" />,
      element: {
        id: '',
        type: 'card',
        props: {
          className: 'bg-white border border-gray-200 rounded-lg shadow-sm p-6'
        },
        children: [
          {
            id: '',
            type: 'heading',
            props: {
              children: 'Card Title',
              className: 'text-xl font-semibold mb-2'
            }
          },
          {
            id: '',
            type: 'paragraph',
            props: {
              children: 'This is a card component with some content.',
              className: 'text-gray-600 mb-4'
            }
          },
          {
            id: '',
            type: 'button',
            props: {
              children: 'Learn More',
              className: 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
            }
          }
        ]
      }
    }
  ];

  // Fix for React 18 compatibility
  useEffect(() => {
    // Disable drag temporarily on mount to prevent React 18 strict mode issues
    setIsDragDisabled(true);
    const timer = setTimeout(() => setIsDragDisabled(false), 100);
    return () => clearTimeout(timer);
  }, []);

  const generateId = () => `element_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

  const assignUniqueIds = (element: Element): Element => {
    const newElement = {
      ...element,
      id: generateId(),
      children: element.children?.map(child => assignUniqueIds(child))
    };
    return newElement;
  };

  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleDragEnd = useCallback((result: DropResult) => {
    setIsDragging(false);

    if (!result.destination || isDragDisabled) return;

    const { source, destination } = result;

    try {
      if (source.droppableId === 'components' && destination.droppableId === 'canvas') {
        // Adding new component from sidebar
        const template = componentTemplates.find(t => t.id === result.draggableId);
        if (template) {
          const newElement = assignUniqueIds(template.element);
          setElements(prev => [...prev, newElement]);
        }
      } else if (source.droppableId === 'canvas' && destination.droppableId === 'canvas') {
        // Reordering within canvas
        const newElements = Array.from(elements);
        const [removed] = newElements.splice(source.index, 1);
        newElements.splice(destination.index, 0, removed);
        setElements(newElements);
      }
    } catch (error) {
      console.error('Drag and drop error:', error);
    }
  }, [elements, isDragDisabled, componentTemplates]);

  const deleteElement = useCallback((elementId: string) => {
    setElements(prev => prev.filter(el => el.id !== elementId));
    if (selectedElement?.id === elementId) {
      setSelectedElement(null);
    }
  }, [selectedElement]);

  const duplicateElement = useCallback((element: Element) => {
    const duplicated = assignUniqueIds(element);
    setElements(prev => [...prev, duplicated]);
  }, []);

  const renderElement = (element: Element, isSelected: boolean = false): React.ReactNode => {
    const commonProps = {
      key: element.id,
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation();
        setSelectedElement(element);
      },
      className: `${element.props.className || ''} ${isSelected ? 'ring-2 ring-blue-500 relative' : ''} cursor-pointer`,
      style: element.props.style
    };

    const elementWithToolbar = (content: React.ReactNode) => {
      if (!isSelected) return content;

      return (
        <div className="relative group">
          {content}
          <div className="absolute -top-8 right-0 flex gap-1 bg-white border border-gray-300 rounded shadow-lg p-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={(e) => {
                e.stopPropagation();
                duplicateElement(element);
              }}
              className="p-1 hover:bg-gray-100 rounded"
              title="Duplicate"
            >
              <Copy className="w-3 h-3" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                deleteElement(element.id);
              }}
              className="p-1 hover:bg-red-100 rounded text-red-600"
              title="Delete"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          </div>
        </div>
      );
    };

    switch (element.type) {
      case 'container':
      case 'form':
      case 'card':
        return elementWithToolbar(
          <div {...commonProps}>
            {element.children?.map(child => renderElement(child))}
            {(!element.children || element.children.length === 0) && (
              <div className="text-gray-400 text-center py-8">
                Drop components here
              </div>
            )}
          </div>
        );
      case 'heading':
        return elementWithToolbar(<h2 {...commonProps}>{element.props.children}</h2>);
      case 'paragraph':
        return elementWithToolbar(<p {...commonProps}>{element.props.children}</p>);
      case 'button':
        return elementWithToolbar(<button {...commonProps}>{element.props.children}</button>);
      case 'image':
        return elementWithToolbar(<img {...commonProps} src={element.props.src} alt={element.props.alt} />);
      case 'input':
        return elementWithToolbar(<input {...commonProps} placeholder={element.props.placeholder} />);
      case 'textarea':
        return elementWithToolbar(<textarea {...commonProps} placeholder={element.props.placeholder} rows={element.props.rows} />);
      case 'list':
        return elementWithToolbar(
          <ul {...commonProps}>
            {element.children?.map(child => renderElement(child))}
          </ul>
        );
      case 'listItem':
        return elementWithToolbar(<li {...commonProps}>{element.props.children}</li>);
      case 'alert':
        const alertIcons = {
          success: <CheckCircle className="w-5 h-5 text-green-400" />,
          error: <XCircle className="w-5 h-5 text-red-400" />,
          warning: <AlertCircle className="w-5 h-5 text-yellow-400" />,
          info: <Info className="w-5 h-5 text-blue-400" />
        };
        return elementWithToolbar(
          <div {...commonProps}>
            <div className="flex items-center">
              {alertIcons[element.props.type as keyof typeof alertIcons]}
              <p className="ml-3">{element.props.message}</p>
            </div>
          </div>
        );
      case 'select':
        return elementWithToolbar(
          <select {...commonProps}>
            {element.props.options?.map((option: string, index: number) => (
              <option key={index} value={option}>{option}</option>
            ))}
          </select>
        );
      case 'checkbox':
        return elementWithToolbar(
          <label {...commonProps}>
            <input type="checkbox" className="mr-2" />
            {element.props.label}
          </label>
        );
      case 'radio':
        return elementWithToolbar(
          <div {...commonProps}>
            {element.props.options?.map((option: string, index: number) => (
              <label key={index} className="flex items-center space-x-2">
                <input type="radio" name={element.props.name} value={option} />
                <span>{option}</span>
              </label>
            ))}
          </div>
        );
      case 'badge':
        return elementWithToolbar(<span {...commonProps}>{element.props.children}</span>);
      case 'divider':
        return elementWithToolbar(<hr {...commonProps} />);
      default:
        return elementWithToolbar(<div {...commonProps}>Unknown element</div>);
    }
  };

  const generateCode = (): string => {
    const elementsToCode = (elements: Element[]): string => {
      return elements.map(element => {
        const props = Object.entries(element.props)
          .filter(([key]) => key !== 'children' && key !== 'style')
          .map(([key, value]) => `${key}="${value}"`)
          .join(' ');
        
        const style = element.props.style 
          ? ` style={${JSON.stringify(element.props.style)}}`
          : '';

        switch (element.type) {
          case 'container':
            const children = element.children ? elementsToCode(element.children) : '';
            return `<div ${props}${style}>\n${children}\n</div>`;
          case 'text':
            return `<p ${props}${style}>${element.props.children}</p>`;
          case 'button':
            return `<button ${props}${style}>${element.props.children}</button>`;
          case 'image':
            return `<img ${props}${style} />`;
          case 'input':
            return `<input ${props}${style} />`;
          default:
            return `<div ${props}${style}>Unknown element</div>`;
        }
      }).join('\n');
    };

    return `import React from 'react';

const GeneratedComponent = () => {
  return (
    <div className="p-4">
${elementsToCode(elements).split('\n').map(line => `      ${line}`).join('\n')}
    </div>
  );
};

export default GeneratedComponent;`;
  };

  const getPreviewWidth = () => {
    switch (previewMode) {
      case 'mobile': return 'max-w-sm';
      case 'tablet': return 'max-w-2xl';
      default: return 'max-w-full';
    }
  };

  return (
    <div className="h-screen flex">
      <DragDropContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        {/* Component Sidebar */}
        <div className="w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto">
          <div className="p-4 border-b border-gray-700">
            <h2 className="text-xl font-semibold text-white">Components</h2>
            <p className="text-gray-400 text-sm">Drag to add</p>
          </div>
          
          <StrictModeDroppable droppableId="components" isDropDisabled={true}>
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="p-4 space-y-2"
              >
                {componentTemplates.map((template, index) => (
                  <Draggable
                    key={template.id}
                    draggableId={template.id}
                    index={index}
                    isDragDisabled={isDragDisabled}
                  >
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className="flex items-center gap-2 p-3 bg-gray-700 rounded-lg cursor-move hover:bg-gray-600"
                      >
                        {template.icon}
                        <span className="text-white">{template.name}</span>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </StrictModeDroppable>
        </div>

        {/* Main Canvas */}
        <div className="flex-1 flex flex-col">
          <div className="bg-gray-800 border-b border-gray-700 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setPreviewMode('desktop')}
                  className={`p-2 rounded ${previewMode === 'desktop' ? 'bg-blue-600' : 'bg-gray-700'}`}
                >
                  <Monitor className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setPreviewMode('tablet')}
                  className={`p-2 rounded ${previewMode === 'tablet' ? 'bg-blue-600' : 'bg-gray-700'}`}
                >
                  <Tablet className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setPreviewMode('mobile')}
                  className={`p-2 rounded ${previewMode === 'mobile' ? 'bg-blue-600' : 'bg-gray-700'}`}
                >
                  <Smartphone className="w-5 h-5" />
                </button>
              </div>
              
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setViewMode('design')}
                  className={`flex items-center gap-2 px-4 py-2 rounded ${
                    viewMode === 'design' ? 'bg-blue-600' : 'bg-gray-700'
                  }`}
                >
                  <Eye className="w-4 h-4" />
                  Design
                </button>
                <button
                  onClick={() => setViewMode('preview')}
                  className={`flex items-center gap-2 px-4 py-2 rounded ${
                    viewMode === 'preview' ? 'bg-blue-600' : 'bg-gray-700'
                  }`}
                >
                  <Eye className="w-4 h-4" />
                  Preview
                </button>
                <button
                  onClick={() => setViewMode('code')}
                  className={`flex items-center gap-2 px-4 py-2 rounded ${
                    viewMode === 'code' ? 'bg-blue-600' : 'bg-gray-700'
                  }`}
                >
                  <Code className="w-4 h-4" />
                  Code
                </button>
              </div>
            </div>
          </div>

          <div className="flex-1 p-8 overflow-auto">
            <div className={`mx-auto ${getPreviewWidth()}`}>
              {viewMode === 'code' ? (
                <pre className="bg-gray-900 p-4 rounded-lg overflow-auto">
                  <code className="text-green-400">{generateCode()}</code>
                </pre>
              ) : (
                <StrictModeDroppable droppableId="canvas" isDropDisabled={isDragDisabled}>
                  {(provided, snapshot) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className={`min-h-[500px] bg-white rounded-lg shadow-lg p-8 transition-colors ${
                        snapshot.isDraggingOver ? 'bg-blue-50 border-2 border-blue-300 border-dashed' : ''
                      }`}
                    >
                      {elements.length === 0 && (
                        <div className="flex items-center justify-center h-64 text-gray-400 border-2 border-dashed border-gray-300 rounded-lg">
                          <div className="text-center">
                            <Square className="w-12 h-12 mx-auto mb-4 opacity-50" />
                            <p>Drag components here to start building</p>
                          </div>
                        </div>
                      )}
                      {elements.map((element, index) => (
                        <Draggable
                          key={element.id}
                          draggableId={element.id}
                          index={index}
                          isDragDisabled={isDragDisabled}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              {renderElement(element, selectedElement?.id === element.id)}
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </StrictModeDroppable>
              )}
            </div>
          </div>
        </div>
      </DragDropContext>
    </div>
  );
};
