
import React from 'react';
import { Code, Package, FileText, CheckSquare, Activity, Clock } from 'lucide-react';

export const Dashboard = () => {
  const stats = [
    { label: 'Files Created', value: '24', icon: FileText, color: 'text-blue-400' },
    { label: 'Components', value: '12', icon: Package, color: 'text-green-400' },
    { label: 'Active Tasks', value: '8', icon: CheckSquare, color: 'text-yellow-400' },
    { label: 'Code Lines', value: '1.2k', icon: Code, color: 'text-purple-400' },
  ];

  const recentActivity = [
    { action: 'Created component', file: 'Button.tsx', time: '2 min ago' },
    { action: 'Generated file', file: 'utils/api.ts', time: '5 min ago' },
    { action: 'Completed task', file: 'Setup routing', time: '10 min ago' },
    { action: 'Updated component', file: 'Header.tsx', time: '15 min ago' },
  ];

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Welcome to Dev Workspace</h1>
        <p className="text-gray-400">Your all-in-one development environment</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <Icon className={`w-8 h-8 ${stat.color}`} />
                <span className="text-2xl font-bold">{stat.value}</span>
              </div>
              <p className="text-gray-400 text-sm">{stat.label}</p>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center gap-2 mb-4">
            <Activity className="w-5 h-5 text-blue-400" />
            <h2 className="text-xl font-semibold">Recent Activity</h2>
          </div>
          <div className="space-y-3">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                <div>
                  <p className="text-white">{activity.action}</p>
                  <p className="text-sm text-gray-400">{activity.file}</p>
                </div>
                <div className="flex items-center gap-1 text-xs text-gray-400">
                  <Clock size={12} />
                  {activity.time}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-2 gap-4">
            <button className="p-4 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
              <Code className="w-6 h-6 mb-2" />
              <span className="text-sm font-medium">New File</span>
            </button>
            <button className="p-4 bg-green-600 hover:bg-green-700 rounded-lg transition-colors">
              <Package className="w-6 h-6 mb-2" />
              <span className="text-sm font-medium">Component</span>
            </button>
            <button className="p-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors">
              <FileText className="w-6 h-6 mb-2" />
              <span className="text-sm font-medium">Generate</span>
            </button>
            <button className="p-4 bg-yellow-600 hover:bg-yellow-700 rounded-lg transition-colors">
              <CheckSquare className="w-6 h-6 mb-2" />
              <span className="text-sm font-medium">Add Task</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
