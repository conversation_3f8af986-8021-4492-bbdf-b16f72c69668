
import React, { useState } from 'react';
import MonacoEditor from '@monaco-editor/react';
import { Play, Save, Download, FileText } from 'lucide-react';

export const CodeEditor = () => {
  const [code, setCode] = useState(`// Welcome to Dev Workspace Code Editor
import React from 'react';

const MyComponent = () => {
  return (
    <div className="p-4">
      <h1>Hello, World!</h1>
      <p>Start coding your amazing project here!</p>
    </div>
  );
};

export default MyComponent;`);

  const [language, setLanguage] = useState('typescript');
  const [fileName, setFileName] = useState('MyComponent.tsx');

  const handleEditorChange = (value: string | undefined) => {
    setCode(value || '');
  };

  const handleSave = () => {
    console.log('Saving file:', fileName);
    // Implement save functionality
  };

  const handleDownload = () => {
    const element = document.createElement('a');
    const file = new Blob([code], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = fileName;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  return (
    <div className="h-screen flex flex-col">
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-400" />
              <input
                type="text"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                className="bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 focus:border-blue-400 outline-none"
              />
            </div>
            <select
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              className="bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 focus:border-blue-400 outline-none"
            >
              <option value="typescript">TypeScript</option>
              <option value="javascript">JavaScript</option>
              <option value="css">CSS</option>
              <option value="html">HTML</option>
              <option value="json">JSON</option>
            </select>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleSave}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
            >
              <Save size={16} />
              Save
            </button>
            <button
              onClick={handleDownload}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
            >
              <Download size={16} />
              Download
            </button>
          </div>
        </div>
      </div>

      <div className="flex-1">
        <MonacoEditor
          height="100%"
          language={language}
          theme="vs-dark"
          value={code}
          onChange={handleEditorChange}
          options={{
            fontSize: 14,
            minimap: { enabled: false },
            wordWrap: 'on',
            automaticLayout: true,
            scrollBeyondLastLine: false,
            renderWhitespace: 'selection',
            bracketPairColorization: { enabled: true },
          }}
        />
      </div>
    </div>
  );
};
