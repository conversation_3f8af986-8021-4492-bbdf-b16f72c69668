
import React, { useState } from 'react';
import { GitBranch, History, Download, Upload, Rewind, FastForward, GitCommit, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';

interface Version {
  id: string;
  version: string;
  title: string;
  description: string;
  author: string;
  timestamp: Date;
  changes: string[];
  isCurrent: boolean;
  tag?: string;
}

interface Branch {
  id: string;
  name: string;
  isActive: boolean;
  lastCommit: Date;
  commits: number;
}

export const ProjectVersioning = () => {
  const [versions, setVersions] = useState<Version[]>([
    {
      id: '1',
      version: 'v1.2.0',
      title: 'Added AI Assistant Features',
      description: 'Implemented AI code generation and review features',
      author: 'You',
      timestamp: new Date(),
      changes: ['Added AI Assistant component', 'Integrated Groq API', 'Added code review functionality'],
      isCurrent: true,
      tag: 'latest'
    },
    {
      id: '2',
      version: 'v1.1.5',
      title: 'UI Component Library Update',
      description: 'Enhanced component library with new templates',
      author: '<PERSON>',
      timestamp: new Date(Date.now() - ********),
      changes: ['Updated Button components', 'Added Card templates', 'Fixed responsive issues'],
      isCurrent: false
    },
    {
      id: '3',
      version: 'v1.1.0',
      title: 'Initial Component System',
      description: 'Basic component library and theme system',
      author: 'You',
      timestamp: new Date(Date.now() - *********),
      changes: ['Created component library', 'Added dark mode', 'Setup basic routing'],
      isCurrent: false,
      tag: 'stable'
    }
  ]);

  const [branches, setBranches] = useState<Branch[]>([
    {
      id: '1',
      name: 'main',
      isActive: true,
      lastCommit: new Date(),
      commits: 15
    },
    {
      id: '2',
      name: 'feature/collaboration',
      isActive: false,
      lastCommit: new Date(Date.now() - 3600000),
      commits: 8
    },
    {
      id: '3',
      name: 'hotfix/styling',
      isActive: false,
      lastCommit: new Date(Date.now() - 7200000),
      commits: 3
    }
  ]);

  const [newCommitMessage, setNewCommitMessage] = useState('');
  const [showCommitDialog, setShowCommitDialog] = useState(false);

  const handleCreateCommit = () => {
    if (newCommitMessage.trim()) {
      const newVersion: Version = {
        id: Date.now().toString(),
        version: `v1.${versions.length + 1}.0`,
        title: newCommitMessage,
        description: 'New version created',
        author: 'You',
        timestamp: new Date(),
        changes: ['Various improvements and fixes'],
        isCurrent: true
      };

      setVersions(prev => [newVersion, ...prev.map(v => ({ ...v, isCurrent: false }))]);
      setNewCommitMessage('');
      setShowCommitDialog(false);
    }
  };

  const handleRollback = (versionId: string) => {
    setVersions(prev => prev.map(v => ({
      ...v,
      isCurrent: v.id === versionId
    })));
  };

  const switchBranch = (branchId: string) => {
    setBranches(prev => prev.map(b => ({
      ...b,
      isActive: b.id === branchId
    })));
  };

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 p-6 overflow-y-auto">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">Project Versioning</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage project versions with Git-like functionality
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Branches */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <GitBranch className="w-5 h-5" />
              Branches
            </h2>
            
            <div className="space-y-2 mb-4">
              {branches.map((branch) => (
                <div
                  key={branch.id}
                  onClick={() => switchBranch(branch.id)}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    branch.isActive
                      ? 'bg-blue-100 border-2 border-blue-500 dark:bg-blue-900'
                      : 'bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{branch.name}</span>
                    {branch.isActive && <Badge variant="secondary">Active</Badge>}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {branch.commits} commits
                  </p>
                </div>
              ))}
            </div>

            <Button className="w-full" variant="outline">
              <GitBranch className="w-4 h-4 mr-2" />
              New Branch
            </Button>
          </div>

          {/* Version History */}
          <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold flex items-center gap-2">
                <History className="w-5 h-5" />
                Version History
              </h2>
              <Button onClick={() => setShowCommitDialog(true)}>
                <GitCommit className="w-4 h-4 mr-2" />
                New Commit
              </Button>
            </div>
            
            <div className="space-y-4">
              {versions.map((version, index) => (
                <div key={version.id} className="border-l-2 border-gray-200 dark:border-gray-600 pl-4 relative">
                  <div className="absolute w-3 h-3 bg-blue-500 rounded-full -left-2 top-2"></div>
                  
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold">{version.title}</h3>
                        <Badge variant="outline">{version.version}</Badge>
                        {version.tag && (
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            <Tag className="w-3 h-3 mr-1" />
                            {version.tag}
                          </Badge>
                        )}
                        {version.isCurrent && (
                          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            Current
                          </Badge>
                        )}
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                        {version.description}
                      </p>
                      <p className="text-xs text-gray-500 mb-2">
                        by {version.author} • {version.timestamp.toLocaleString()}
                      </p>
                      <div className="space-y-1">
                        {version.changes.map((change, idx) => (
                          <p key={idx} className="text-sm text-gray-600 dark:text-gray-400">
                            • {change}
                          </p>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex gap-2 ml-4">
                      {!version.isCurrent && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleRollback(version.id)}
                        >
                          <Rewind className="w-4 h-4" />
                        </Button>
                      )}
                      <Button size="sm" variant="outline">
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
            
            <div className="space-y-3">
              <Button className="w-full" variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export Project
              </Button>
              
              <Button className="w-full" variant="outline">
                <Upload className="w-4 h-4 mr-2" />
                Import Version
              </Button>
              
              <Button className="w-full" variant="outline">
                <GitBranch className="w-4 h-4 mr-2" />
                Merge Branch
              </Button>
              
              <Button className="w-full" variant="outline">
                <Tag className="w-4 h-4 mr-2" />
                Create Tag
              </Button>
            </div>

            <div className="mt-6 pt-4 border-t">
              <h3 className="font-medium mb-2">Statistics</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Total Commits:</span>
                  <span className="font-medium">{versions.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Active Branches:</span>
                  <span className="font-medium">{branches.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Current Version:</span>
                  <span className="font-medium">
                    {versions.find(v => v.isCurrent)?.version}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Commit Dialog */}
        {showCommitDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96">
              <h3 className="text-lg font-semibold mb-4">Create New Commit</h3>
              <Input
                placeholder="Commit message"
                value={newCommitMessage}
                onChange={(e) => setNewCommitMessage(e.target.value)}
                className="mb-4"
              />
              <Textarea
                placeholder="Detailed description (optional)"
                className="mb-4"
              />
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setShowCommitDialog(false)}
                >
                  Cancel
                </Button>
                <Button 
                  className="flex-1"
                  onClick={handleCreateCommit}
                >
                  Create Commit
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
