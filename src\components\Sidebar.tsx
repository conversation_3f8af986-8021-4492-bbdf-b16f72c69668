
import React from 'react';
import { 
  Home, 
  Code, 
  Package, 
  FileText, 
  CheckSquare, 
  ChevronLeft,
  ChevronRight,
  Palette,
  Layers,
  Search,
  Bot,
  Server,
  Users,
  GitBranch,
  MousePointer,
  FolderOpen,
  Puzzle,
  ShoppingBag
} from 'lucide-react';

interface SidebarProps {
  activeView: string;
  setActiveView: (view: any) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  activeView, 
  setActiveView, 
  isOpen, 
  onToggle 
}) => {
  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'projects', label: 'Projects', icon: FolderOpen },
    { id: 'design-system', label: 'Design System', icon: Palette },
    { id: 'components', label: 'Components', icon: Package },
    { id: 'snippets', label: 'Code Snippets', icon: Search },
    { id: 'ai-assistant', label: 'AI Assistant', icon: Bot },
    { id: 'api-mocking', label: 'API Mocking', icon: Server },
    { id: 'drag-drop', label: 'Page Builder', icon: MousePointer },
    { id: 'editor', label: 'Code Editor', icon: Code },
    { id: 'generator', label: 'File Generator', icon: FileText },
    { id: 'tasks', label: 'Tasks', icon: CheckSquare },
    { id: 'collaboration', label: 'Collaboration', icon: Users },
    { id: 'versioning', label: 'Versioning', icon: GitBranch },
    { id: 'plugins', label: 'Plugins', icon: Puzzle },
    { id: 'marketplace', label: 'Marketplace', icon: ShoppingBag },
  ];

  return (
    <div className={`fixed left-0 top-0 h-full bg-gray-800 border-r border-gray-700 transition-all duration-300 z-40 ${
      isOpen ? 'w-64' : 'w-16'
    }`}>
      <div className="p-4 border-b border-gray-700 flex items-center justify-between">
        {isOpen && (
          <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
            Dev Workspace
          </h1>
        )}
        <button
          onClick={onToggle}
          className="p-2 rounded-lg hover:bg-gray-700 transition-colors"
        >
          {isOpen ? <ChevronLeft size={20} /> : <ChevronRight size={20} />}
        </button>
      </div>
      
      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.id}>
                <button
                  onClick={() => setActiveView(item.id)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                    activeView === item.id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  <Icon size={20} />
                  {isOpen && <span className="font-medium">{item.label}</span>}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
};
