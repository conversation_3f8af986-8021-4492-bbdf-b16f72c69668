
import React, { useState } from 'react';
import { Puzzle, Download, Settings, Star, Shield, Zap, Package, Search, Filter } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';

interface Plugin {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  category: 'ui' | 'tools' | 'integration' | 'ai' | 'productivity';
  rating: number;
  downloads: number;
  isInstalled: boolean;
  isEnabled: boolean;
  icon: string;
  screenshots: string[];
  features: string[];
}

export const PluginSystem = () => {
  const [plugins, setPlugins] = useState<Plugin[]>([
    {
      id: '1',
      name: 'Advanced Code Formatter',
      description: 'Automatically format code with advanced rules and customization options',
      version: '2.1.0',
      author: 'DevTools Inc',
      category: 'tools',
      rating: 4.8,
      downloads: 12500,
      isInstalled: true,
      isEnabled: true,
      icon: '🎨',
      screenshots: ['https://via.placeholder.com/400x250'],
      features: ['Auto formatting', 'Custom rules', 'Multi-language support']
    },
    {
      id: '2',
      name: 'AI Component Generator',
      description: 'Generate React components using AI with natural language descriptions',
      version: '1.5.2',
      author: 'AI Labs',
      category: 'ai',
      rating: 4.9,
      downloads: 8200,
      isInstalled: true,
      isEnabled: false,
      icon: '🤖',
      screenshots: ['https://via.placeholder.com/400x250'],
      features: ['Natural language input', 'Smart component generation', 'TypeScript support']
    },
    {
      id: '3',
      name: 'Theme Designer Pro',
      description: 'Advanced theme customization with live preview and export options',
      version: '3.0.1',
      author: 'Design Systems',
      category: 'ui',
      rating: 4.7,
      downloads: 15600,
      isInstalled: false,
      isEnabled: false,
      icon: '🎭',
      screenshots: ['https://via.placeholder.com/400x250'],
      features: ['Live preview', 'Color palette generator', 'Export to CSS/SCSS']
    },
    {
      id: '4',
      name: 'Git Integration Plus',
      description: 'Enhanced Git workflow with visual branching and merge conflict resolution',
      version: '4.2.0',
      author: 'Version Control Co',
      category: 'tools',
      rating: 4.6,
      downloads: 9800,
      isInstalled: false,
      isEnabled: false,
      icon: '🌿',
      screenshots: ['https://via.placeholder.com/400x250'],
      features: ['Visual branching', 'Merge conflict resolution', 'Commit templates']
    },
    {
      id: '5',
      name: 'Performance Monitor',
      description: 'Real-time performance monitoring and optimization suggestions',
      version: '1.8.3',
      author: 'Performance Labs',
      category: 'productivity',
      rating: 4.4,
      downloads: 6700,
      isInstalled: true,
      isEnabled: true,
      icon: '⚡',
      screenshots: ['https://via.placeholder.com/400x250'],
      features: ['Real-time monitoring', 'Performance suggestions', 'Bundle analysis']
    },
    {
      id: '6',
      name: 'Database Designer',
      description: 'Visual database schema designer with SQL generation',
      version: '2.3.1',
      author: 'DB Tools',
      category: 'integration',
      rating: 4.5,
      downloads: 4300,
      isInstalled: false,
      isEnabled: false,
      icon: '🗄️',
      screenshots: ['https://via.placeholder.com/400x250'],
      features: ['Visual schema design', 'SQL generation', 'Multiple DB support']
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | Plugin['category']>('all');
  const [showInstalled, setShowInstalled] = useState(false);
  const [selectedPlugin, setSelectedPlugin] = useState<Plugin | null>(null);

  const categories = [
    { id: 'all', name: 'All', icon: Package },
    { id: 'ui', name: 'UI/UX', icon: Puzzle },
    { id: 'tools', name: 'Tools', icon: Settings },
    { id: 'integration', name: 'Integration', icon: Zap },
    { id: 'ai', name: 'AI', icon: Shield },
    { id: 'productivity', name: 'Productivity', icon: Star }
  ];

  const filteredPlugins = plugins.filter(plugin => {
    const matchesSearch = plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plugin.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || plugin.category === selectedCategory;
    const matchesInstalled = !showInstalled || plugin.isInstalled;
    return matchesSearch && matchesCategory && matchesInstalled;
  });

  const togglePlugin = (pluginId: string) => {
    setPlugins(prev => prev.map(plugin =>
      plugin.id === pluginId ? { ...plugin, isEnabled: !plugin.isEnabled } : plugin
    ));
  };

  const installPlugin = (pluginId: string) => {
    setPlugins(prev => prev.map(plugin =>
      plugin.id === pluginId ? { ...plugin, isInstalled: true, isEnabled: true } : plugin
    ));
  };

  const uninstallPlugin = (pluginId: string) => {
    setPlugins(prev => prev.map(plugin =>
      plugin.id === pluginId ? { ...plugin, isInstalled: false, isEnabled: false } : plugin
    ));
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'ui': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'tools': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'integration': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'ai': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'productivity': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold mb-4">Plugin System</h1>
          
          {/* Search and Filters */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search plugins..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="w-4 h-4" />
              </Button>
            </div>
            
            <div className="flex items-center gap-2">
              <Switch
                checked={showInstalled}
                onCheckedChange={setShowInstalled}
                id="show-installed"
              />
              <label htmlFor="show-installed" className="text-sm">Installed only</label>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Categories Sidebar */}
        <div className="w-64 bg-white dark:bg-gray-800 border-r p-4">
          <h2 className="font-semibold mb-4">Categories</h2>
          <div className="space-y-1">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id as any)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  {category.name}
                </button>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="max-w-6xl mx-auto">
            {/* Plugin Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPlugins.map((plugin) => (
                <div key={plugin.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{plugin.icon}</div>
                        <div>
                          <h3 className="font-semibold text-lg">{plugin.name}</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">v{plugin.version}</p>
                        </div>
                      </div>
                      {plugin.isInstalled && (
                        <Switch
                          checked={plugin.isEnabled}
                          onCheckedChange={() => togglePlugin(plugin.id)}
                        />
                      )}
                    </div>

                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                      {plugin.description}
                    </p>

                    <div className="flex items-center gap-2 mb-4">
                      <Badge className={getCategoryColor(plugin.category)}>
                        {plugin.category}
                      </Badge>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        {plugin.rating}
                      </div>
                      <span className="text-sm text-gray-500">
                        {plugin.downloads.toLocaleString()} downloads
                      </span>
                    </div>

                    <div className="space-y-2 mb-4">
                      {plugin.features.slice(0, 2).map((feature, idx) => (
                        <div key={idx} className="text-sm text-gray-600 dark:text-gray-400">
                          • {feature}
                        </div>
                      ))}
                    </div>

                    <div className="flex gap-2">
                      {plugin.isInstalled ? (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1"
                            onClick={() => setSelectedPlugin(plugin)}
                          >
                            <Settings className="w-4 h-4 mr-2" />
                            Configure
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => uninstallPlugin(plugin.id)}
                          >
                            Uninstall
                          </Button>
                        </>
                      ) : (
                        <Button
                          className="flex-1"
                          onClick={() => installPlugin(plugin.id)}
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Install
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Empty State */}
            {filteredPlugins.length === 0 && (
              <div className="text-center py-12">
                <Puzzle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No plugins found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Try adjusting your search terms or category filter
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Plugin Detail Modal */}
      {selectedPlugin && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-96 overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{selectedPlugin.icon}</span>
                  <div>
                    <h3 className="text-xl font-semibold">{selectedPlugin.name}</h3>
                    <p className="text-gray-600 dark:text-gray-400">by {selectedPlugin.author}</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setSelectedPlugin(null)}
                >
                  Close
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-gray-600 dark:text-gray-400">{selectedPlugin.description}</p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Features</h4>
                  <ul className="space-y-1">
                    {selectedPlugin.features.map((feature, idx) => (
                      <li key={idx} className="text-gray-600 dark:text-gray-400">• {feature}</li>
                    ))}
                  </ul>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Version:</span> {selectedPlugin.version}
                  </div>
                  <div>
                    <span className="font-medium">Downloads:</span> {selectedPlugin.downloads.toLocaleString()}
                  </div>
                  <div>
                    <span className="font-medium">Rating:</span> {selectedPlugin.rating}/5
                  </div>
                  <div>
                    <span className="font-medium">Category:</span> {selectedPlugin.category}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
