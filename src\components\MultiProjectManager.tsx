
import React, { useState } from 'react';
import { Plus, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Clock, Users, Download, Trash2, Copy, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';

interface Project {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  lastModified: Date;
  isStarred: boolean;
  collaborators: string[];
  status: 'active' | 'archived' | 'template';
  framework: 'React' | 'Vue' | 'Angular';
  size: string;
}

export const MultiProjectManager = () => {
  const [projects, setProjects] = useState<Project[]>([
    {
      id: '1',
      name: 'Dev Workspace',
      description: 'Advanced development workspace with AI integration',
      thumbnail: 'https://via.placeholder.com/300x200/3B82F6/white?text=Dev+Workspace',
      lastModified: new Date(),
      isStarred: true,
      collaborators: ['👨‍💻', '👩‍💻', '👨‍🎨'],
      status: 'active',
      framework: 'React',
      size: '45.2 MB'
    },
    {
      id: '2',
      name: 'E-commerce Dashboard',
      description: 'Modern dashboard for e-commerce management',
      thumbnail: 'https://via.placeholder.com/300x200/10B981/white?text=Dashboard',
      lastModified: new Date(Date.now() - 86400000),
      isStarred: false,
      collaborators: ['👩‍💼', '👨‍💻'],
      status: 'active',
      framework: 'React',
      size: '32.1 MB'
    },
    {
      id: '3',
      name: 'Landing Page Template',
      description: 'Reusable landing page template',
      thumbnail: 'https://via.placeholder.com/300x200/F59E0B/white?text=Template',
      lastModified: new Date(Date.now() - 172800000),
      isStarred: true,
      collaborators: ['👨‍🎨'],
      status: 'template',
      framework: 'React',
      size: '18.5 MB'
    },
    {
      id: '4',
      name: 'Mobile App Prototype',
      description: 'Prototype for mobile application',
      thumbnail: 'https://via.placeholder.com/300x200/8B5CF6/white?text=Mobile',
      lastModified: new Date(Date.now() - 259200000),
      isStarred: false,
      collaborators: ['👨‍💻', '👩‍🎨', '👨‍💼'],
      status: 'archived',
      framework: 'React',
      size: '28.7 MB'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'archived' | 'template'>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newProjectDescription, setNewProjectDescription] = useState('');

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || project.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const toggleStar = (projectId: string) => {
    setProjects(prev => prev.map(project =>
      project.id === projectId ? { ...project, isStarred: !project.isStarred } : project
    ));
  };

  const deleteProject = (projectId: string) => {
    setProjects(prev => prev.filter(project => project.id !== projectId));
  };

  const duplicateProject = (project: Project) => {
    const newProject: Project = {
      ...project,
      id: Date.now().toString(),
      name: `${project.name} (Copy)`,
      lastModified: new Date(),
      status: 'active'
    };
    setProjects(prev => [newProject, ...prev]);
  };

  const createProject = () => {
    if (newProjectName.trim()) {
      const newProject: Project = {
        id: Date.now().toString(),
        name: newProjectName,
        description: newProjectDescription,
        thumbnail: 'https://via.placeholder.com/300x200/6B7280/white?text=New+Project',
        lastModified: new Date(),
        isStarred: false,
        collaborators: ['👨‍💻'],
        status: 'active',
        framework: 'React',
        size: '0 MB'
      };
      setProjects(prev => [newProject, ...prev]);
      setNewProjectName('');
      setNewProjectDescription('');
      setShowCreateDialog(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'archived': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      case 'template': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 p-6 overflow-y-auto">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold mb-2">My Projects</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Manage all your development projects in one place
              </p>
            </div>
            <Button onClick={() => setShowCreateDialog(true)} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="w-4 h-4 mr-2" />
              New Project
            </Button>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              {['all', 'active', 'archived', 'template'].map((status) => (
                <Button
                  key={status}
                  variant={filterStatus === status ? 'default' : 'outline'}
                  onClick={() => setFilterStatus(status as any)}
                  className="capitalize"
                >
                  {status}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProjects.map((project) => (
            <div key={project.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              {/* Thumbnail */}
              <div className="relative">
                <img 
                  src={project.thumbnail} 
                  alt={project.name}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-2 right-2 flex gap-1">
                  <Button
                    size="icon"
                    variant="secondary"
                    className="w-8 h-8"
                    onClick={() => toggleStar(project.id)}
                  >
                    <Star className={`w-4 h-4 ${project.isStarred ? 'fill-yellow-400 text-yellow-400' : ''}`} />
                  </Button>
                </div>
                <div className="absolute top-2 left-2">
                  <Badge className={getStatusColor(project.status)}>
                    {project.status}
                  </Badge>
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="font-semibold text-lg mb-2 truncate">{project.name}</h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                  {project.description}
                </p>

                {/* Project Info */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Clock className="w-4 h-4" />
                    {project.lastModified.toLocaleDateString()}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Users className="w-4 h-4" />
                    <div className="flex -space-x-1">
                      {project.collaborators.map((avatar, idx) => (
                        <Avatar key={idx} className="w-6 h-6 border-2 border-white dark:border-gray-800">
                          {avatar}
                        </Avatar>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Button className="flex-1" size="sm">
                    <FolderOpen className="w-4 h-4 mr-2" />
                    Open
                  </Button>
                  <Button variant="outline" size="icon" onClick={() => duplicateProject(project)}>
                    <Copy className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="icon" onClick={() => deleteProject(project.id)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <FolderOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No projects found</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {searchTerm ? 'Try adjusting your search terms' : 'Create your first project to get started'}
            </p>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create New Project
            </Button>
          </div>
        )}

        {/* Create Project Dialog */}
        {showCreateDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96">
              <h3 className="text-lg font-semibold mb-4">Create New Project</h3>
              <Input
                placeholder="Project name"
                value={newProjectName}
                onChange={(e) => setNewProjectName(e.target.value)}
                className="mb-4"
              />
              <Input
                placeholder="Project description"
                value={newProjectDescription}
                onChange={(e) => setNewProjectDescription(e.target.value)}
                className="mb-4"
              />
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setShowCreateDialog(false)}
                >
                  Cancel
                </Button>
                <Button 
                  className="flex-1"
                  onClick={createProject}
                >
                  Create Project
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
