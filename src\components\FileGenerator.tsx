
import React, { useState } from 'react';
import { Plus, Download, FileText, Folder } from 'lucide-react';

interface FileTemplate {
  id: string;
  name: string;
  description: string;
  extension: string;
  template: string;
  fields: { name: string; label: string; placeholder: string; required: boolean }[];
}

export const FileGenerator = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<FileTemplate | null>(null);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [generatedCode, setGeneratedCode] = useState<string>('');

  const templates: FileTemplate[] = [
    {
      id: 'react-component',
      name: 'React Component',
      description: 'Generate a functional React component with TypeScript',
      extension: '.tsx',
      template: `import React from 'react';

interface {{componentName}}Props {
  // Add your props here
}

export const {{componentName}}: React.FC<{{componentName}}Props> = () => {
  return (
    <div className="{{className}}">
      <h1>{{componentName}}</h1>
      <p>{{description}}</p>
    </div>
  );
};

export default {{componentName}};`,
      fields: [
        { name: 'componentName', label: 'Component Name', placeholder: 'MyComponent', required: true },
        { name: 'description', label: 'Description', placeholder: 'Component description', required: false },
        { name: 'className', label: 'CSS Class', placeholder: 'my-component', required: false }
      ]
    },
    {
      id: 'api-service',
      name: 'API Service',
      description: 'Generate an API service with TypeScript',
      extension: '.ts',
      template: `class {{serviceName}}Service {
  private baseUrl = '{{baseUrl}}';

  async get{{entityName}}(): Promise<{{entityName}}[]> {
    const response = await fetch(\`\${this.baseUrl}/{{endpoint}}\`);
    if (!response.ok) {
      throw new Error('Failed to fetch {{entityName}}');
    }
    return response.json();
  }

  async create{{entityName}}(data: Partial<{{entityName}}>): Promise<{{entityName}}> {
    const response = await fetch(\`\${this.baseUrl}/{{endpoint}}\`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error('Failed to create {{entityName}}');
    }
    return response.json();
  }
}

export const {{serviceName}}Service = new {{serviceName}}Service();`,
      fields: [
        { name: 'serviceName', label: 'Service Name', placeholder: 'User', required: true },
        { name: 'entityName', label: 'Entity Name', placeholder: 'User', required: true },
        { name: 'baseUrl', label: 'Base URL', placeholder: '/api', required: true },
        { name: 'endpoint', label: 'Endpoint', placeholder: 'users', required: true }
      ]
    },
    {
      id: 'utility-function',
      name: 'Utility Function',
      description: 'Generate a utility function with TypeScript',
      extension: '.ts',
      template: `/**
 * {{description}}
 * @param {{paramName}} - {{paramDescription}}
 * @returns {{returnDescription}}
 */
export const {{functionName}} = ({{paramName}}: {{paramType}}): {{returnType}} => {
  // TODO: Implement your logic here
  return {{defaultReturn}};
};

export default {{functionName}};`,
      fields: [
        { name: 'functionName', label: 'Function Name', placeholder: 'formatDate', required: true },
        { name: 'description', label: 'Description', placeholder: 'Formats a date string', required: false },
        { name: 'paramName', label: 'Parameter Name', placeholder: 'date', required: true },
        { name: 'paramType', label: 'Parameter Type', placeholder: 'string', required: true },
        { name: 'paramDescription', label: 'Parameter Description', placeholder: 'The date to format', required: false },
        { name: 'returnType', label: 'Return Type', placeholder: 'string', required: true },
        { name: 'returnDescription', label: 'Return Description', placeholder: 'The formatted date', required: false },
        { name: 'defaultReturn', label: 'Default Return', placeholder: '""', required: true }
      ]
    }
  ];

  const generateCode = () => {
    if (!selectedTemplate) return;

    let code = selectedTemplate.template;
    Object.entries(formData).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      code = code.replace(regex, value || '');
    });
    setGeneratedCode(code);
  };

  const downloadFile = () => {
    if (!generatedCode || !selectedTemplate) return;

    const fileName = `${formData.componentName || formData.serviceName || formData.functionName || 'generated'}${selectedTemplate.extension}`;
    const element = document.createElement('a');
    const file = new Blob([generatedCode], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = fileName;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  return (
    <div className="h-screen flex">
      <div className="w-1/3 bg-gray-800 border-r border-gray-700 overflow-y-auto">
        <div className="p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold">File Generator</h2>
          <p className="text-gray-400 text-sm">Generate boilerplate code quickly</p>
        </div>
        
        <div className="p-4">
          {templates.map((template) => (
            <div
              key={template.id}
              onClick={() => {
                setSelectedTemplate(template);
                setFormData({});
                setGeneratedCode('');
              }}
              className={`p-4 rounded-lg cursor-pointer transition-colors mb-2 ${
                selectedTemplate?.id === template.id
                  ? 'bg-blue-600'
                  : 'bg-gray-700 hover:bg-gray-600'
              }`}
            >
              <div className="flex items-center gap-2 mb-2">
                <FileText className="w-4 h-4" />
                <h3 className="font-medium">{template.name}</h3>
              </div>
              <p className="text-sm text-gray-400">{template.description}</p>
              <span className="text-xs text-blue-400 mt-1 block">{template.extension}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        {selectedTemplate ? (
          <>
            <div className="bg-gray-800 border-b border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold">{selectedTemplate.name}</h3>
                  <p className="text-gray-400">{selectedTemplate.description}</p>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={generateCode}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                  >
                    <Plus size={16} />
                    Generate
                  </button>
                  {generatedCode && (
                    <button
                      onClick={downloadFile}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
                    >
                      <Download size={16} />
                      Download
                    </button>
                  )}
                </div>
              </div>
            </div>

            <div className="flex-1 flex">
              <div className="w-1/2 p-6 border-r border-gray-700">
                <h4 className="text-lg font-semibold mb-4">Configuration</h4>
                <div className="space-y-4">
                  {selectedTemplate.fields.map((field) => (
                    <div key={field.name}>
                      <label className="block text-sm font-medium mb-2">
                        {field.label}
                        {field.required && <span className="text-red-400 ml-1">*</span>}
                      </label>
                      <input
                        type="text"
                        placeholder={field.placeholder}
                        value={formData[field.name] || ''}
                        onChange={(e) => handleInputChange(field.name, e.target.value)}
                        className="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-blue-400 outline-none"
                      />
                    </div>
                  ))}
                </div>
              </div>

              <div className="w-1/2 p-6">
                <h4 className="text-lg font-semibold mb-4">Generated Code</h4>
                {generatedCode ? (
                  <pre className="bg-gray-900 p-4 rounded-lg overflow-auto h-96">
                    <code className="text-green-400">{generatedCode}</code>
                  </pre>
                ) : (
                  <div className="flex items-center justify-center h-96 bg-gray-900 rounded-lg">
                    <p className="text-gray-400">Fill the form and click Generate to see the code</p>
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Folder className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Select a Template</h3>
              <p className="text-gray-400">Choose a file template to start generating code</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
