
import React, { useState } from 'react';
import { Download, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/hooks/use-toast';

interface ExportOptions {
  includeNodeModules: boolean;
  includeGitFiles: boolean;
  includeBuildFiles: boolean;
  includeDevDependencies: boolean;
}

export const ProjectExport = () => {
  const [isExporting, setIsExporting] = useState(false);
  const [options, setOptions] = useState<ExportOptions>({
    includeNodeModules: false,
    includeGitFiles: false,
    includeBuildFiles: false,
    includeDevDependencies: true,
  });

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Export Successful",
        description: "Your project has been exported as a ZIP file.",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "There was an error exporting your project.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Download className="h-4 w-4" />
          Export Project
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Export Project
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="nodeModules"
                checked={options.includeNodeModules}
                onCheckedChange={(checked) => 
                  setOptions(prev => ({ ...prev, includeNodeModules: !!checked }))
                }
              />
              <label htmlFor="nodeModules" className="text-sm font-medium">
                Include node_modules (larger file size)
              </label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="gitFiles"
                checked={options.includeGitFiles}
                onCheckedChange={(checked) => 
                  setOptions(prev => ({ ...prev, includeGitFiles: !!checked }))
                }
              />
              <label htmlFor="gitFiles" className="text-sm font-medium">
                Include Git files (.git, .gitignore)
              </label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="buildFiles"
                checked={options.includeBuildFiles}
                onCheckedChange={(checked) => 
                  setOptions(prev => ({ ...prev, includeBuildFiles: !!checked }))
                }
              />
              <label htmlFor="buildFiles" className="text-sm font-medium">
                Include build files (dist, build)
              </label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="devDependencies"
                checked={options.includeDevDependencies}
                onCheckedChange={(checked) => 
                  setOptions(prev => ({ ...prev, includeDevDependencies: !!checked }))
                }
              />
              <label htmlFor="devDependencies" className="text-sm font-medium">
                Include dev dependencies in package.json
              </label>
            </div>
          </div>
          
          <Button 
            onClick={handleExport} 
            disabled={isExporting}
            className="w-full"
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export ZIP
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
