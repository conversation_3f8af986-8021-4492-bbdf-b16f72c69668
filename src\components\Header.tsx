
import React from 'react';
import { ThemeToggle } from './ThemeToggle';
import { ProjectExport } from './ProjectExport';
import { NotificationCenter } from './NotificationCenter';
import { HotkeyCustomizer } from './HotkeyCustomizer';
import { GlobalSearch } from './GlobalSearch';

export const Header = () => {
  return (
    <header className="h-16 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-6 flex items-center justify-between">
      <div className="flex items-center gap-4">
        <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
          Dev Workspace
        </h1>
        <GlobalSearch />
      </div>
      
      <div className="flex items-center gap-2">
        <HotkeyCustomizer />
        <ProjectExport />
        <NotificationCenter />
        <ThemeToggle />
      </div>
    </header>
  );
};
