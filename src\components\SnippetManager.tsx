
import React, { useState } from 'react';
import { Search, Plus, Copy, Edit, Trash2, Tag, Code, Save } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface Snippet {
  id: string;
  title: string;
  description: string;
  code: string;
  language: string;
  tags: string[];
  createdAt: Date;
}

export const SnippetManager = () => {
  const [snippets, setSnippets] = useState<Snippet[]>([
    {
      id: '1',
      title: 'React Hook useState',
      description: 'Basic useState hook example',
      code: `const [count, setCount] = useState(0);

const increment = () => {
  setCount(count + 1);
};`,
      language: 'javascript',
      tags: ['react', 'hooks', 'state'],
      createdAt: new Date()
    },
    {
      id: '2',
      title: 'CSS Flexbox Center',
      description: 'Center content with flexbox',
      code: `.center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}`,
      language: 'css',
      tags: ['css', 'flexbox', 'center'],
      createdAt: new Date()
    },
    {
      id: '3',
      title: 'TypeScript Interface',
      description: 'Basic TypeScript interface definition',
      code: `interface User {
  id: number;
  name: string;
  email: string;
  isActive: boolean;
}

const user: User = {
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>',
  isActive: true
};`,
      language: 'typescript',
      tags: ['typescript', 'interface', 'types'],
      createdAt: new Date()
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSnippet, setSelectedSnippet] = useState<Snippet | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    title: '',
    description: '',
    code: '',
    language: 'javascript',
    tags: ''
  });

  const filteredSnippets = snippets.filter(snippet =>
    snippet.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    snippet.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    snippet.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const startEdit = (snippet?: Snippet) => {
    if (snippet) {
      setEditForm({
        title: snippet.title,
        description: snippet.description,
        code: snippet.code,
        language: snippet.language,
        tags: snippet.tags.join(', ')
      });
      setSelectedSnippet(snippet);
    } else {
      setEditForm({
        title: '',
        description: '',
        code: '',
        language: 'javascript',
        tags: ''
      });
      setSelectedSnippet(null);
    }
    setIsEditing(true);
  };

  const saveSnippet = () => {
    const tagsArray = editForm.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    
    if (selectedSnippet) {
      // Update existing snippet
      setSnippets(snippets.map(snippet =>
        snippet.id === selectedSnippet.id
          ? {
              ...snippet,
              title: editForm.title,
              description: editForm.description,
              code: editForm.code,
              language: editForm.language,
              tags: tagsArray
            }
          : snippet
      ));
    } else {
      // Create new snippet
      const newSnippet: Snippet = {
        id: Date.now().toString(),
        title: editForm.title,
        description: editForm.description,
        code: editForm.code,
        language: editForm.language,
        tags: tagsArray,
        createdAt: new Date()
      };
      setSnippets([newSnippet, ...snippets]);
    }
    
    setIsEditing(false);
    setSelectedSnippet(null);
  };

  const deleteSnippet = (id: string) => {
    setSnippets(snippets.filter(snippet => snippet.id !== id));
    if (selectedSnippet?.id === id) {
      setSelectedSnippet(null);
    }
  };

  return (
    <div className="h-screen flex">
      <div className="w-1/3 bg-gray-800 border-r border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Code Snippets</h2>
            <button
              onClick={() => startEdit()}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
            >
              <Plus size={16} />
              New
            </button>
          </div>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <Input
              className="pl-10"
              placeholder="Search snippets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4 space-y-2">
          {filteredSnippets.map((snippet) => (
            <div
              key={snippet.id}
              onClick={() => setSelectedSnippet(snippet)}
              className={`p-4 rounded-lg cursor-pointer transition-colors ${
                selectedSnippet?.id === snippet.id
                  ? 'bg-blue-600'
                  : 'bg-gray-700 hover:bg-gray-600'
              }`}
            >
              <h3 className="font-medium mb-1">{snippet.title}</h3>
              <p className="text-sm text-gray-400 mb-2">{snippet.description}</p>
              <div className="flex flex-wrap gap-1">
                {snippet.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-gray-600 rounded text-xs"
                  >
                    <Tag size={10} />
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        {isEditing ? (
          <div className="flex-1 p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold mb-2">
                {selectedSnippet ? 'Edit Snippet' : 'New Snippet'}
              </h3>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="snippet-title">Title</Label>
                <Input
                  id="snippet-title"
                  value={editForm.title}
                  onChange={(e) => setEditForm({...editForm, title: e.target.value})}
                  placeholder="Snippet title"
                />
              </div>

              <div>
                <Label htmlFor="snippet-description">Description</Label>
                <Input
                  id="snippet-description"
                  value={editForm.description}
                  onChange={(e) => setEditForm({...editForm, description: e.target.value})}
                  placeholder="Brief description"
                />
              </div>

              <div>
                <Label htmlFor="snippet-language">Language</Label>
                <select
                  id="snippet-language"
                  value={editForm.language}
                  onChange={(e) => setEditForm({...editForm, language: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-400 outline-none"
                >
                  <option value="javascript">JavaScript</option>
                  <option value="typescript">TypeScript</option>
                  <option value="css">CSS</option>
                  <option value="html">HTML</option>
                  <option value="python">Python</option>
                  <option value="json">JSON</option>
                </select>
              </div>

              <div>
                <Label htmlFor="snippet-tags">Tags (comma separated)</Label>
                <Input
                  id="snippet-tags"
                  value={editForm.tags}
                  onChange={(e) => setEditForm({...editForm, tags: e.target.value})}
                  placeholder="react, hooks, state"
                />
              </div>

              <div>
                <Label htmlFor="snippet-code">Code</Label>
                <Textarea
                  id="snippet-code"
                  value={editForm.code}
                  onChange={(e) => setEditForm({...editForm, code: e.target.value})}
                  placeholder="Your code here..."
                  className="min-h-64 font-mono"
                />
              </div>

              <div className="flex gap-2">
                <button
                  onClick={saveSnippet}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
                >
                  <Save size={16} />
                  Save
                </button>
                <button
                  onClick={() => setIsEditing(false)}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        ) : selectedSnippet ? (
          <>
            <div className="bg-gray-800 border-b border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold">{selectedSnippet.title}</h3>
                  <p className="text-gray-400">{selectedSnippet.description}</p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedSnippet.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center gap-1 px-2 py-1 bg-gray-600 rounded text-xs"
                      >
                        <Tag size={10} />
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => startEdit(selectedSnippet)}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                  >
                    <Edit size={16} />
                    Edit
                  </button>
                  <button
                    onClick={() => copyToClipboard(selectedSnippet.code)}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
                  >
                    <Copy size={16} />
                    Copy
                  </button>
                  <button
                    onClick={() => deleteSnippet(selectedSnippet.id)}
                    className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded transition-colors"
                  >
                    <Trash2 size={16} />
                    Delete
                  </button>
                </div>
              </div>
            </div>

            <div className="flex-1 p-6">
              <pre className="bg-gray-900 p-4 rounded-lg overflow-auto h-full">
                <code className="text-green-400">{selectedSnippet.code}</code>
              </pre>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Code className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Select a Snippet</h3>
              <p className="text-gray-400">Choose a code snippet to view and edit</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
